import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timezone
import time
import os
import json

# Initialize connection to MT5
if not mt5.initialize():
    print("Failed to connect to MT5")
    mt5.shutdown()
    exit()

def get_mt5_server_time():
    """Get MT5 server time in UTC"""
    account_info = mt5.account_info()
    if account_info is not None:
        return datetime.now(timezone.utc)
    return datetime.now(timezone.utc)

def determine_price_action(candle_data):
    """Determine if candle is bullish, bearish, or doji"""
    open_price = candle_data['open']
    close_price = candle_data['close']
    if close_price > open_price:
        return "BULLISH 🟢"
    elif close_price < open_price:
        return "BEARISH 🔴"
    else:
        return "DOJI ➖"

def format_candle_times(candle_time, timeframe):
    """Calculate candle start and end times"""
    start_time = pd.to_datetime(candle_time, unit='s')
    duration_minutes = {
        mt5.TIMEFRAME_M1: 1,
        mt5.TIMEFRAME_M5: 5,
        mt5.TIMEFRAME_M15: 15,
        mt5.TIMEFRAME_M30: 30,
        mt5.TIMEFRAME_H1: 60
    }.get(timeframe, 5)
    end_time = start_time + pd.Timedelta(minutes=duration_minutes)
    return start_time, end_time

def get_historical_data(symbol, timeframe, num_candles=1000):
    """Fetch historical candles"""
    now = datetime.now(timezone.utc)
    rates = mt5.copy_rates_from(symbol, timeframe, now, num_candles)
    if rates is None or len(rates) == 0:
        print("No historical data retrieved")
        return None
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df

def save_data_to_file(df, symbol, timeframe):
    """Save historical data to a CSV file in the current directory"""
    timeframe_str = {
        mt5.TIMEFRAME_M1: "M1",
        mt5.TIMEFRAME_M5: "M5",
        mt5.TIMEFRAME_M15: "M15",
        mt5.TIMEFRAME_M30: "M30",
        mt5.TIMEFRAME_H1: "H1"
    }.get(timeframe, "M5")
    filename = f"{symbol}_{timeframe_str}_data.csv"
    filepath = os.path.join(os.getcwd(), filename)
    df.to_csv(filepath, index=False)
    print(f"💾 Historical data saved to: {filepath}")
    print(f"📊 Total candles saved: {len(df)}")
    return filepath

def get_latest_completed_candles(symbol, timeframe, num_candles=2, delay_seconds=2):
    """Fetch the latest completed candles"""
    time.sleep(delay_seconds)
    now = datetime.now(timezone.utc)
    minute_divisors = {
        mt5.TIMEFRAME_M1: 1,
        mt5.TIMEFRAME_M5: 5,
        mt5.TIMEFRAME_M15: 15,
        mt5.TIMEFRAME_M30: 30,
        mt5.TIMEFRAME_H1: 60
    }
    divisor = minute_divisors.get(timeframe, 5)
    minutes = now.minute // divisor * divisor
    start_time = datetime(now.year, now.month, now.day, now.hour, minutes, tzinfo=timezone.utc)
    rates = mt5.copy_rates_from(symbol, timeframe, start_time, num_candles + 1)
    if rates is None or len(rates) == 0:
        print("No data retrieved for analysis")
        return None
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    completed_candles = df.tail(num_candles + 1).head(num_candles)
    return completed_candles.iloc[::-1].reset_index(drop=True)

def analyze_candles(candles_df, timeframe):
    """Analyze candles and return structured data"""
    current_time = get_mt5_server_time()
    analysis = []
    for idx, candle in candles_df.iterrows():
        start_time, end_time = format_candle_times(candle['time'], timeframe)
        price_action = determine_price_action(candle)
        body_size = abs(candle['close'] - candle['open'])
        candle_range = candle['high'] - candle['low']
        candle_analysis = {
            "candle_number": idx + 1,
            "label": "LATEST" if idx == 0 else "PREVIOUS",
            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "price_action": price_action,
            "open": candle['open'],
            "high": candle['high'],
            "low": candle['low'],
            "close": candle['close'],
            "volume": candle['tick_volume'],
            "spread": candle['spread'],
            "body_size": body_size,
            "range": candle_range
        }
        analysis.append(candle_analysis)
    
    summary = {
        "latest_candle": analysis[0]["price_action"] if analysis else None,
        "previous_candle": analysis[1]["price_action"] if len(analysis) > 1 else None,
        "trend": None
    }
    if len(analysis) > 1:
        if analysis[0]['close'] > analysis[1]['close']:
            summary["trend"] = "UPWARD TREND 📈"
        elif analysis[0]['close'] < analysis[1]['close']:
            summary["trend"] = "DOWNWARD TREND 📉"
        else:
            summary["trend"] = "SIDEWAYS 🔄"
    
    return {
        "analysis_time": current_time.strftime('%Y-%m-%d %H:%M:%S UTC'),
        "candles": analysis,
        "summary": summary
    }

def display_candle_analysis(analysis_data):
    """Display formatted candle analysis"""
    print("=" * 80)
    print(f"🕐 ANALYSIS TIME: {analysis_data['analysis_time']}")
    print("=" * 80)
    if analysis_data['candles']:
        print(f"\n📈 LATEST {len(analysis_data['candles'])} COMPLETED CANDLES")
        print("-" * 80)
        for candle in analysis_data['candles']:
            print(f"\n🕯️  CANDLE #{candle['candle_number']} ({candle['label']})")
            print(f"   Time Period: {candle['start_time']} → {candle['end_time'].split(' ')[1]}")
            print(f"   Price Action: {candle['price_action']}")
            print(f"   OPEN:  {candle['open']:.5f}")
            print(f"   HIGH:  {candle['high']:.5f}")
            print(f"   LOW:   {candle['low']:.5f}")
            print(f"   CLOSE: {candle['close']:.5f}")
            print(f"   Volume: {candle['volume']}")
            print(f"   Spread: {candle['spread']}")
            print(f"   Body Size: {candle['body_size']:.5f}")
            print(f"   Range: {candle['range']:.5f}")
            print("-" * 40)
        print(f"\n📊 SUMMARY:")
        print(f"   Latest Candle (Candle 1): {analysis_data['summary']['latest_candle']}")
        if analysis_data['summary']['previous_candle']:
            print(f"   Previous Candle (Candle 2): {analysis_data['summary']['previous_candle']}")
        if analysis_data['summary']['trend']:
            print(f"   Short-term Trend: {analysis_data['summary']['trend']}")
        else:
            print("   Not enough data for trend analysis")
    print("=" * 80)

def save_candle_analysis(analysis_data, symbol, timeframe):
    """Save each candle's analysis to separate JSON files"""
    timeframe_str = {
        mt5.TIMEFRAME_M1: "M1",
        mt5.TIMEFRAME_M5: "M5",
        mt5.TIMEFRAME_M15: "M15",
        mt5.TIMEFRAME_M30: "M30",
        mt5.TIMEFRAME_H1: "H1"
    }.get(timeframe, "M5")
    filepaths = {}
    for candle in analysis_data['candles']:
        candle_num = candle['candle_number']
        filename = f"{symbol}_{timeframe_str}_candle_{candle_num}_analysis.json"
        filepath = os.path.join(os.getcwd(), filename)
        candle_data = {
            "analysis_time": analysis_data['analysis_time'],
            "candle": candle,
            "summary": analysis_data['summary']
        }
        with open(filepath, 'w') as f:
            json.dump(candle_data, f, indent=4)
        print(f"💾 Candle {candle_num} analysis saved to: {filepath}")
        filepaths[f"candle_{candle_num}"] = filepath
    return filepaths

def collect_and_analyze_data(symbol, timeframe, historical_candles=1000, analysis_candles=2):
    """Collect historical data and analyze latest candles"""
    print("🔄 COLLECTING HISTORICAL DATA...")
    print("-" * 50)
    
    historical_data = get_historical_data(symbol, timeframe, historical_candles)
    if historical_data is not None:
        historical_filepath = save_data_to_file(historical_data, symbol, timeframe)
        print(f"📈 Data range: {historical_data['time'].min()} to {historical_data['time'].max()}")
        print()
        
        print("🔍 PERFORMING CANDLE ANALYSIS...")
        latest_candles = get_latest_completed_candles(symbol, timeframe, analysis_candles)
        if latest_candles is not None:
            analysis_data = analyze_candles(latest_candles, timeframe)
            display_candle_analysis(analysis_data)
            analysis_filepaths = save_candle_analysis(analysis_data, symbol, timeframe)
            return historical_filepath, analysis_filepaths
        else:
            print("❌ Failed to get latest candles for analysis")
            return historical_filepath, None
    else:
        print("❌ Failed to collect historical data")
        return None, None

if __name__ == "__main__":
    symbol = "EURJPY"
    timeframe = mt5.TIMEFRAME_M5
    historical_candles = 1000
    analysis_candles = 2
    
    historical_filepath, analysis_filepaths = collect_and_analyze_data(
        symbol, timeframe, historical_candles, analysis_candles
    )
    
    if historical_filepath and analysis_filepaths:
        print(f"\n✅ Process completed successfully!")
        print(f"📁 Historical data: {historical_filepath}")
        print(f"📁 Candle 1 analysis: {analysis_filepaths.get('candle_1', 'Not saved')}")
        print(f"📁 Candle 2 analysis: {analysis_filepaths.get('candle_2', 'Not saved')}")
    
    mt5.shutdown()