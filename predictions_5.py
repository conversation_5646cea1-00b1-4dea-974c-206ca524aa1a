"""
Fibonacci Bounce Predictor - Modular Library

A refactored, modular library for predicting Fibonacci bounce patterns in financial data.
This module provides both programmatic API access and direct script execution capabilities.

When retrieving raw data, we filter out the latest candle because it's still forming
and its OHLC data can't be used reliably by the model. We use data only up to the
latest complete candle.

Usage:
    # Programmatic usage
    from predictions_5 import FibonacciBouncePredictor, PredictorConfig

    config = PredictorConfig(model_path="model.keras", data_file="data.csv")
    predictor = FibonacciBouncePredictor(config)
    result = predictor.predict()

    # Direct script execution
    python predictions_5.py
"""
import os
import sys
import warnings
import pandas as pd
import numpy as np
import pickle
import json
import tensorflow as tf
from datetime import datetime, timedelta
import glob
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, field
import logging

# Suppress warnings and TensorFlow logs
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')


# Custom Exceptions
class PredictorError(Exception):
    """Base exception for predictor errors"""
    pass


class ModelLoadError(PredictorError):
    """Raised when model loading fails"""
    pass


class DataError(PredictorError):
    """Raised when data processing fails"""
    pass


class FeatureError(PredictorError):
    """Raised when feature engineering fails"""
    pass


@dataclass
class PredictorConfig:
    """Configuration class for the Fibonacci Bounce Predictor"""
    model_path: str = "final_dl_model.keras"
    pipeline_path: str = "feature_engineering_pipeline.pkl"
    features_path: str = "selected_features.json"
    data_file: Optional[str] = None
    symbol: str = "EURJPY"
    results_file: str = "prediction_results.json"
    verbose: bool = True
    auto_find_data: bool = True
    logging_level: str = "INFO"

class SymbolicFeatureGenerator:
    """
    Generates symbolic features using mathematical operations on existing features.

    This class applies predefined feature recipes to create new features through
    mathematical operations. It supports unary, binary, and ternary operations.
    """

    def __init__(self, verbose: bool = True, logger: Optional[logging.Logger] = None):
        """
        Initialize the symbolic feature generator.

        Args:
            verbose: Whether to print warning messages
            logger: Optional logger instance for structured logging
        """
        self.feature_recipes = []
        self.new_feature_names = []
        self.verbose = verbose
        self.logger = logger or logging.getLogger(__name__)
        self._warnings = []  # Store warnings for programmatic access

    def _apply_operation(self, recipe: Dict[str, Any], x, y=None, cond=None):
        """
        Apply a mathematical operation defined in the recipe.

        Args:
            recipe: Dictionary containing operation details
            x: Primary feature data
            y: Secondary feature data (for binary/ternary operations)
            cond: Condition data (for ternary operations)

        Returns:
            Result of the operation or NaN array on error
        """
        try:
            operation = eval(recipe['operation_str'])
            if recipe.get('is_ternary', False):
                return operation(cond, x, y)
            elif recipe['is_binary']:
                return operation(x, y)
            else:
                return operation(x, None)
        except Exception as e:
            warning_msg = f"Error in operation {recipe.get('name', 'unknown')}: {e}"
            self._log_warning(warning_msg)
            return np.full_like(x, np.nan) if hasattr(x, '__len__') else np.nan

    def _log_warning(self, message: str):
        """Log warning message and store for later access"""
        self._warnings.append(message)
        if self.verbose:
            print(f"⚠️ Warning: {message}")
        self.logger.warning(message)

    def get_warnings(self) -> List[str]:
        """Get list of warnings generated during transformation"""
        return self._warnings.copy()

    def clear_warnings(self):
        """Clear stored warnings"""
        self._warnings.clear()

    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform input DataFrame by applying all feature recipes.

        Args:
            X: Input DataFrame with features

        Returns:
            DataFrame with original features plus generated symbolic features

        Raises:
            FeatureError: If transformation fails critically
        """
        if not isinstance(X, pd.DataFrame):
            raise FeatureError("Input must be a pandas DataFrame")

        self.clear_warnings()
        X_copy = X.copy()

        for recipe in self.feature_recipes:
            try:
                self._apply_recipe(recipe, X_copy)
            except Exception as e:
                error_msg = f"Critical error computing {recipe.get('name', 'unknown')}: {e}"
                self._log_warning(error_msg)
                X_copy[recipe['name']] = np.nan

        # Replace infinite values with NaN
        result = X_copy.replace([np.inf, -np.inf], np.nan)

        if self.verbose and self._warnings:
            print(f"⚠️ Generated {len(self._warnings)} warnings during feature transformation")

        return result

    def _apply_recipe(self, recipe: Dict[str, Any], X_copy: pd.DataFrame):
        """Apply a single feature recipe to the DataFrame"""
        if recipe.get('is_ternary', False):
            required = [recipe['condition'], recipe['feature1'], recipe['feature2']]
            if all(f in X_copy.columns for f in required):
                X_copy[recipe['name']] = self._apply_operation(
                    recipe,
                    X_copy[recipe['feature1']],
                    X_copy[recipe['feature2']],
                    X_copy[recipe['condition']]
                )
            else:
                missing = [f for f in required if f not in X_copy.columns]
                self._log_warning(f"Missing features {missing} for {recipe['name']}")
                X_copy[recipe['name']] = np.nan

        elif recipe['is_binary']:
            required = [recipe['feature1'], recipe['feature2']]
            if all(f in X_copy.columns for f in required):
                X_copy[recipe['name']] = self._apply_operation(
                    recipe,
                    X_copy[recipe['feature1']],
                    X_copy[recipe['feature2']]
                )
            else:
                missing = [f for f in required if f not in X_copy.columns]
                self._log_warning(f"Missing features {missing} for {recipe['name']}")
                X_copy[recipe['name']] = np.nan
        else:
            if recipe['feature1'] in X_copy.columns:
                X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']])
            else:
                self._log_warning(f"Missing feature {recipe['feature1']} for {recipe['name']}")
                X_copy[recipe['name']] = np.nan

class FeatureEngineeringPipeline:
    """
    Complete feature engineering pipeline for financial data.

    This class handles the creation of base features, scaling, and symbolic feature generation
    for financial time series data. It provides both training and inference capabilities.
    """

    def __init__(self, verbose: bool = True, logger: Optional[logging.Logger] = None):
        """
        Initialize the feature engineering pipeline.

        Args:
            verbose: Whether to print progress messages
            logger: Optional logger instance for structured logging
        """
        self.scaler = None
        self.selected_features = []
        self.symbolic_generator = None
        self.pipeline_config = {}
        self.verbose = verbose
        self.logger = logger or logging.getLogger(__name__)
        self._feature_creation_stats = {}

    def create_base_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create base technical analysis features from OHLC data.

        Args:
            df: DataFrame with OHLC data (must have 'open', 'high', 'low', 'close' columns)

        Returns:
            DataFrame with original data plus engineered features

        Raises:
            DataError: If required columns are missing or data is invalid
        """
        if self.verbose:
            print("🔧 Creating base features...")
        self.logger.info("Starting base feature creation")

        if not isinstance(df, pd.DataFrame):
            raise DataError("Input must be a pandas DataFrame")

        data = df.copy()

        # Validate required columns
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise DataError(f"Missing required columns: {missing_cols}")

        # Handle NaN values in raw data
        nan_count = data.isna().sum().sum()
        if nan_count > 0:
            warning_msg = f"NaN values detected in raw data: {nan_count} values"
            if self.verbose:
                print(f"⚠️ Warning: {warning_msg}")
            self.logger.warning(warning_msg)
            data = data.fillna(method='ffill').fillna(method='bfill')

        # Track feature creation progress
        initial_features = len(data.columns)

        try:
            # Create swing and Fibonacci features
            data = self._create_swing_features(data)
            data = self._create_fibonacci_features(data)
            data = self._create_support_resistance_features(data)
            data = self._create_confluence_features(data)
            data = self._create_trend_momentum_features(data)
            data = self._create_moving_average_features(data)
            data = self._create_volatility_features(data)
            data = self._create_candlestick_features(data)
            data = self._create_session_features(data)
            data = self._create_volume_features(data)

            # Final cleanup
            data = self._cleanup_features(data)

            # Store statistics
            final_features = len(data.columns)
            self._feature_creation_stats = {
                'initial_features': initial_features,
                'final_features': final_features,
                'created_features': final_features - initial_features,
                'data_rows': len(data)
            }

            if self.verbose:
                print(f"✅ Created {final_features - initial_features} features from {len(data)} rows")
            self.logger.info(f"Feature creation completed: {self._feature_creation_stats}")

            return data

        except Exception as e:
            error_msg = f"Error during feature creation: {str(e)}"
            self.logger.error(error_msg)
            raise FeatureError(error_msg) from e

    def _create_swing_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create swing high/low features for different periods"""
        for period in [20, 50, 100]:
            data[f'swing_high_{period}'] = data['high'].rolling(period, min_periods=1).max()
            data[f'swing_low_{period}'] = data['low'].rolling(period, min_periods=1).min()
            data[f'swing_range_{period}'] = data[f'swing_high_{period}'] - data[f'swing_low_{period}']
        return data

    def _create_fibonacci_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create Fibonacci retracement level features"""
        for period in [20, 50, 100]:
            for level in [0.236, 0.382, 0.5, 0.618, 0.786]:
                level_str = int(level * 1000)
                data[f'fib_{level_str}_{period}'] = (
                    data[f'swing_high_{period}'] - level * data[f'swing_range_{period}']
                )
                data[f'distance_to_fib_{level_str}_{period}'] = (
                    (data['close'] - data[f'fib_{level_str}_{period}']) / data['close']
                )
                data[f'abs_distance_to_fib_{level_str}_{period}'] = abs(
                    data[f'distance_to_fib_{level_str}_{period}']
                )
        return data

    def _create_support_resistance_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create support/resistance features based on Fibonacci touches"""
        for period in [20, 50]:
            for level in [0.382, 0.618]:
                level_str = int(level * 1000)
                col_name = f'fib_touches_{level_str}_{period}'
                data[col_name] = data[f'abs_distance_to_fib_{level_str}_{period}'].rolling(
                    period, min_periods=1
                ).apply(lambda x: (x < 0.0005).sum(), raw=False)
        return data

    def _create_confluence_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create confluence score features"""
        data['fib_confluence_score'] = 0
        for period in [20, 50]:
            for level in [0.382, 0.5, 0.618]:
                level_str = int(level * 1000)
                data['fib_confluence_score'] += (
                    data[f'abs_distance_to_fib_{level_str}_{period}'] < 0.001
                ).astype(int)
        return data

    def _create_trend_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create trend and momentum features"""
        # Trend strength
        data['trend_strength_10'] = (data['close'] - data['close'].shift(10)) / data['close'].shift(10)
        data['trend_strength_20'] = (data['close'] - data['close'].shift(20)) / data['close'].shift(20)

        # Momentum
        for period in [3, 5, 10]:
            data[f'momentum_{period}'] = data['close'] - data['close'].shift(period)
        data['momentum_decreasing'] = (abs(data['momentum_3']) < abs(data['momentum_5'])).astype(int)

        return data

    def _create_moving_average_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create moving average features"""
        for period in [10, 20, 50]:
            data[f'sma_{period}'] = data['close'].rolling(period, min_periods=1).mean()
            data[f'above_sma_{period}'] = (data['close'] > data[f'sma_{period}']).astype(int)

        data['sma_alignment_bullish'] = (
            (data['sma_10'] > data['sma_20']) & (data['sma_20'] > data['sma_50'])
        ).astype(int)
        data['sma_alignment_bearish'] = (
            (data['sma_10'] < data['sma_20']) & (data['sma_20'] < data['sma_50'])
        ).astype(int)

        return data

    def _create_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create volatility-based features"""
        data['atr_14'] = (data['high'] - data['low']).rolling(14, min_periods=1).mean()
        data['current_range'] = data['high'] - data['low']
        data['volatility_ratio'] = data['current_range'] / data['atr_14']
        data['recent_volatility_expansion'] = (
            data['volatility_ratio'].rolling(3, min_periods=1).max() > 1.5
        ).astype(int)

        return data

    def _create_candlestick_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create candlestick pattern features"""
        data['body_size'] = abs(data['close'] - data['open'])
        data['upper_shadow'] = data['high'] - data[['open', 'close']].max(axis=1)
        data['lower_shadow'] = data[['open', 'close']].min(axis=1) - data['low']
        data['total_range'] = data['high'] - data['low']
        data['hammer_pattern'] = (
            (data['lower_shadow'] > data['body_size'] * 2) &
            (data['upper_shadow'] < data['body_size'] * 0.5)
        ).astype(int)
        data['doji_pattern'] = (data['body_size'] / data['total_range'] < 0.1).astype(int)

        return data

    def _create_session_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create trading session features"""
        data['hour'] = data.index.hour
        data['london_session'] = data['hour'].between(8, 16).astype(int)
        data['ny_session'] = data['hour'].between(13, 21).astype(int)
        data['session_overlap'] = (data['london_session'] & data['ny_session']).astype(int)

        return data

    def _create_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create volume-based features if volume data is available"""
        if 'volume' in data.columns:
            data['volume_ma_20'] = data['volume'].rolling(20, min_periods=1).mean()
            data['volume_ratio'] = data['volume'] / data['volume_ma_20']
            data['volume_confirmation'] = (data['volume_ratio'] > 1.2).astype(int)
        else:
            warning_msg = "'volume' column missing, skipping volume features"
            if self.verbose:
                print(f"⚠️ Warning: {warning_msg}")
            self.logger.warning(warning_msg)

        return data

    def _cleanup_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean up features by handling infinite values and NaNs"""
        # Replace infinite values with NaN
        data = data.replace([np.inf, -np.inf], np.nan)

        # Fill NaN values
        data = data.fillna(method='ffill').fillna(method='bfill').fillna(0)

        return data

    def get_feature_stats(self) -> Dict[str, Any]:
        """Get statistics about the last feature creation process"""
        return self._feature_creation_stats.copy()

    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform input data using the fitted pipeline.

        Args:
            X: Input DataFrame with features

        Returns:
            Transformed DataFrame ready for model prediction

        Raises:
            FeatureError: If pipeline is not fitted or transformation fails
        """
        if self.scaler is None:
            raise FeatureError("Pipeline not fitted. Load the pipeline first.")

        if not isinstance(X, pd.DataFrame):
            raise FeatureError("Input must be a pandas DataFrame")

        try:
            # Define columns to exclude from scaling
            feature_columns_to_exclude = [
                'target', 'next_fib_bounce', 'future_open', 'timestamp', 'timestamp_original'
            ]

            # Determine feature columns for scaling
            if 'feature_columns_used_for_scaling' in self.pipeline_config:
                feature_cols = [
                    col for col in self.pipeline_config['feature_columns_used_for_scaling']
                    if col in X.columns
                ]
            else:
                feature_cols = [
                    col for col in X.columns
                    if col not in feature_columns_to_exclude and
                    X[col].dtype in [np.float64, np.int64]
                ]

            # Scale features
            X_scaled = pd.DataFrame(
                self.scaler.transform(X[feature_cols]),
                columns=feature_cols,
                index=X.index
            )

            # Apply symbolic feature generation
            X_with_symbolic = self.symbolic_generator.transform(X_scaled)

            # Check for missing features
            missing_features = [f for f in self.selected_features if f not in X_with_symbolic.columns]
            if missing_features:
                raise FeatureError(f"Missing features: {missing_features}")

            # Select final features
            X_final = X_with_symbolic[self.selected_features]

            # Handle any remaining NaN values
            nan_count = X_final.isna().sum().sum()
            if nan_count > 0:
                warning_msg = f"NaN values found in final features: {nan_count} values, filling with 0"
                if self.verbose:
                    print(f"⚠️ Warning: {warning_msg}")
                self.logger.warning(warning_msg)
                X_final = X_final.fillna(0)

            return X_final

        except Exception as e:
            error_msg = f"Error during feature transformation: {str(e)}"
            self.logger.error(error_msg)
            raise FeatureError(error_msg) from e

    @classmethod
    def load(cls, filepath: str, verbose: bool = True, logger: Optional[logging.Logger] = None):
        """
        Load a fitted pipeline from file.

        Args:
            filepath: Path to the pipeline pickle file
            verbose: Whether to print loading messages
            logger: Optional logger instance

        Returns:
            Loaded FeatureEngineeringPipeline instance

        Raises:
            ModelLoadError: If loading fails
        """
        if verbose:
            print(f"📥 Loading pipeline from {filepath}")

        if logger is None:
            logger = logging.getLogger(__name__)

        try:
            if not os.path.exists(filepath):
                raise ModelLoadError(f"Pipeline file not found: {filepath}")

            with open(filepath, 'rb') as f:
                pipeline_data = pickle.load(f)

            pipeline = cls(verbose=verbose, logger=logger)
            pipeline.scaler = pipeline_data['scaler']
            pipeline.selected_features = pipeline_data['selected_features']
            pipeline.symbolic_generator = pipeline_data['symbolic_generator']
            pipeline.pipeline_config = pipeline_data['pipeline_config']

            if verbose:
                print(f"✅ Pipeline loaded with {len(pipeline.selected_features)} features")
            logger.info(f"Pipeline loaded successfully from {filepath}")

            return pipeline

        except Exception as e:
            error_msg = f"Failed to load pipeline from {filepath}: {str(e)}"
            logger.error(error_msg)
            raise ModelLoadError(error_msg) from e

@dataclass
class PredictionResult:
    """Data class for prediction results"""
    prediction_time: str
    symbol: str
    prediction: int
    probability: float
    prediction_based_on_candle: str
    prediction_candle_close: float
    latest_price: float
    latest_candle_time: str
    incomplete_candle_filtered: bool
    error: Optional[str] = None
    warnings: List[str] = field(default_factory=list)


class FibonacciBouncePredictor:
    """
    Main predictor class for Fibonacci bounce pattern prediction.

    This class provides both programmatic API access and direct script execution
    capabilities for predicting Fibonacci bounce patterns in financial data.
    """

    def __init__(self, config: Optional[PredictorConfig] = None):
        """
        Initialize the Fibonacci Bounce Predictor.

        Args:
            config: Configuration object. If None, uses default configuration.
        """
        self.config = config or PredictorConfig()
        self.model = None
        self.pipeline = None
        self.selected_features = None
        self.data = None
        self.logger = self._setup_logger()

        # Initialize with configuration
        self.symbol = self.config.symbol
        self.data_file = self.config.data_file

        # Check files if auto-discovery is enabled
        if self.config.auto_find_data or self.config.verbose:
            self._check_files()

    def _setup_logger(self) -> logging.Logger:
        """Setup logger based on configuration"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        logger.setLevel(getattr(logging, self.config.logging_level.upper()))

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _check_files(self) -> Dict[str, bool]:
        """
        Check if required files exist and return status.

        Returns:
            Dictionary with file existence status
        """
        file_status = {}

        # Check for model file
        model_exists = os.path.exists(self.config.model_path)
        file_status['model'] = model_exists
        if not model_exists and self.config.verbose:
            print(f"❌ Model file not found: {self.config.model_path}")

        # Check for pipeline file
        pipeline_exists = os.path.exists(self.config.pipeline_path)
        file_status['pipeline'] = pipeline_exists
        if not pipeline_exists and self.config.verbose:
            print(f"❌ Pipeline file not found: {self.config.pipeline_path}")

        # Check for features file
        features_exists = os.path.exists(self.config.features_path)
        file_status['features'] = features_exists
        if not features_exists and self.config.verbose:
            print(f"❌ Features file not found: {self.config.features_path}")

        # Find CSV data file if not specified
        if self.config.auto_find_data and not self.data_file:
            csv_files = glob.glob(os.path.join(os.getcwd(), '*.csv'))
            if csv_files:
                self.data_file = csv_files[0]
                self.config.data_file = self.data_file
                file_status['data'] = True
                if self.config.verbose:
                    print(f"✅ Found data file: {os.path.basename(self.data_file)}")
            else:
                file_status['data'] = False
                if self.config.verbose:
                    print("❌ No CSV data file found")
        else:
            file_status['data'] = bool(self.data_file and os.path.exists(self.data_file))

        return file_status

    def load_model_and_pipeline(self) -> bool:
        """
        Load the model, pipeline, and features.

        Returns:
            True if all components loaded successfully, False otherwise

        Raises:
            ModelLoadError: If critical loading errors occur
        """
        try:
            if self.config.verbose:
                print("\n🚀 Loading model and pipeline...")
            self.logger.info("Starting model and pipeline loading")

            # Load model
            if not os.path.exists(self.config.model_path):
                error_msg = f"Model file not found: {self.config.model_path}"
                self.logger.error(error_msg)
                if self.config.verbose:
                    print(f"❌ {error_msg}")
                raise ModelLoadError(error_msg)

            self.model = tf.keras.models.load_model(self.config.model_path)
            if self.config.verbose:
                print(f"✅ Model loaded: {self.config.model_path}")
            self.logger.info(f"Model loaded from {self.config.model_path}")

            # Load pipeline
            if not os.path.exists(self.config.pipeline_path):
                error_msg = f"Pipeline file not found: {self.config.pipeline_path}"
                self.logger.error(error_msg)
                if self.config.verbose:
                    print(f"❌ {error_msg}")
                raise ModelLoadError(error_msg)

            self.pipeline = FeatureEngineeringPipeline.load(
                self.config.pipeline_path,
                verbose=self.config.verbose,
                logger=self.logger
            )
            if self.config.verbose:
                print(f"✅ Pipeline loaded: {self.config.pipeline_path}")

            # Load features
            if os.path.exists(self.config.features_path):
                with open(self.config.features_path, 'r') as f:
                    self.selected_features = json.load(f)
                self.pipeline.selected_features = self.selected_features
                if self.config.verbose:
                    print(f"✅ Features loaded: {len(self.selected_features)} features")
                self.logger.info(f"Features loaded: {len(self.selected_features)} features")
            else:
                warning_msg = f"Features file not found: {self.config.features_path}"
                self.logger.warning(warning_msg)
                if self.config.verbose:
                    print(f"⚠️ Warning: {warning_msg}")

            return True

        except ModelLoadError:
            raise
        except Exception as e:
            error_msg = f"Error loading model and pipeline: {str(e)}"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ {error_msg}")
            raise ModelLoadError(error_msg) from e

    def load_data(self, data_file: Optional[str] = None) -> bool:
        """
        Load data from CSV file with proper volume handling.

        Args:
            data_file: Optional path to data file. If None, uses config data_file.

        Returns:
            True if data loaded successfully, False otherwise

        Raises:
            DataError: If data loading fails critically
        """
        # Use provided file or fall back to config
        file_path = data_file or self.data_file

        if not file_path or not os.path.exists(file_path):
            error_msg = f"Data file not found: {file_path}"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ {error_msg}")
            raise DataError(error_msg)

        if self.config.verbose:
            print(f"📊 Loading data from: {os.path.basename(file_path)}")
        self.logger.info(f"Loading data from {file_path}")

        try:
            # Load CSV data
            df = pd.read_csv(file_path)
            if self.config.verbose:
                print(f"✅ Data loaded: {len(df)} rows")
            self.logger.info(f"Data loaded: {len(df)} rows")

            # Validate and process data
            df = self._validate_and_process_data(df)

            # Extract symbol from filename if possible
            filename = os.path.basename(file_path)
            if '_' in filename:
                extracted_symbol = filename.split('_')[0]
                if extracted_symbol != self.symbol:
                    self.symbol = extracted_symbol
                    self.config.symbol = extracted_symbol

            self.data = df
            if self.config.verbose:
                print(f"✅ Data processed: {len(df)} candles")
            self.logger.info(f"Data processing completed: {len(df)} candles")

            return True

        except DataError:
            raise
        except Exception as e:
            error_msg = f"Error loading data: {str(e)}"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ {error_msg}")
            raise DataError(error_msg) from e

    def _validate_and_process_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and process the loaded data"""
        # Check required columns
        required_cols = ['time', 'open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise DataError(f"Missing required columns: {missing_cols}")

        # Convert time to datetime and set as index
        df['time'] = pd.to_datetime(df['time'])
        df = df.set_index('time')

        # Handle volume columns
        if 'tick_volume' in df.columns:
            df['volume'] = df['tick_volume']
        elif 'real_volume' in df.columns:
            df['volume'] = df['real_volume']

        # Keep only OHLCV columns
        columns_to_keep = ['open', 'high', 'low', 'close']
        if 'volume' in df.columns:
            columns_to_keep.append('volume')

        df = df[columns_to_keep]

        # Handle any missing values
        nan_count = df.isna().sum().sum()
        if nan_count > 0:
            warning_msg = f"NaN values detected in data: {nan_count} values"
            self.logger.warning(warning_msg)
            if self.config.verbose:
                print(f"⚠️ Warning: {warning_msg}")
            df = df.fillna(method='ffill').fillna(method='bfill')

        return df

    def load_data_from_dataframe(self, df: pd.DataFrame, symbol: Optional[str] = None) -> bool:
        """
        Load data from a pandas DataFrame.

        Args:
            df: DataFrame with OHLC data
            symbol: Optional symbol name

        Returns:
            True if data loaded successfully

        Raises:
            DataError: If data validation fails
        """
        try:
            if not isinstance(df, pd.DataFrame):
                raise DataError("Input must be a pandas DataFrame")

            # Validate and process data
            processed_df = self._validate_and_process_data(df.copy())

            if symbol:
                self.symbol = symbol
                self.config.symbol = symbol

            self.data = processed_df

            if self.config.verbose:
                print(f"✅ Data loaded from DataFrame: {len(processed_df)} candles")
            self.logger.info(f"Data loaded from DataFrame: {len(processed_df)} candles")

            return True

        except Exception as e:
            error_msg = f"Error loading data from DataFrame: {str(e)}"
            self.logger.error(error_msg)
            raise DataError(error_msg) from e

    def get_data(self, count: Optional[int] = None) -> Optional[pd.DataFrame]:
        """
        Get data from loaded dataset.

        Args:
            count: Optional number of recent candles to return

        Returns:
            DataFrame with requested data or None if no data loaded

        Raises:
            DataError: If data access fails
        """
        if not hasattr(self, 'data') or self.data is None:
            error_msg = "No data loaded"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ {error_msg}")
            raise DataError(error_msg)

        try:
            if count is not None:
                if count <= 0:
                    raise DataError("Count must be positive")
                data = self.data.tail(count).copy()
            else:
                data = self.data.copy()

            return data

        except Exception as e:
            error_msg = f"Error getting data: {str(e)}"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ {error_msg}")
            raise DataError(error_msg) from e

    def filter_incomplete_candle(self, data: pd.DataFrame) -> tuple[pd.DataFrame, bool]:
        """
        Filter out the incomplete current candle and use the last complete candle.

        Args:
            data: DataFrame with OHLC data

        Returns:
            Tuple of (filtered_data, was_filtered)
        """
        if len(data) < 2:
            warning_msg = "Insufficient data for candle filtering"
            self.logger.warning(warning_msg)
            return data, False

        # Get timing information
        latest_data_time = data.index[-1]
        last_candle_time = data.index[-1]

        # Calculate candle interval (assumes uniform intervals)
        candle_interval = data.index[-1] - data.index[-2]
        expected_completion = last_candle_time + candle_interval

        if self.config.verbose:
            print(f"\n🕐 Current time: {latest_data_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📊 Last candle time: {last_candle_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏰ Expected completion: {expected_completion.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⚠️  Last candle is assumed INCOMPLETE (current candle still forming)")
            print(f"✅ Using previous complete candle for prediction")

        self.logger.info(f"Filtering incomplete candle. Last candle: {last_candle_time}")

        # Remove the last (incomplete) candle and use the second-to-last
        data_filtered = data[:-1].copy()
        used_candle_time = data_filtered.index[-1]

        if self.config.verbose:
            print(f"📍 Using candle from: {used_candle_time.strftime('%Y-%m-%d %H:%M:%S')}")

        self.logger.info(f"Using candle from: {used_candle_time}")

        return data_filtered, True

    def make_prediction(self, data: Optional[pd.DataFrame] = None) -> PredictionResult:
        """
        Make a prediction using the loaded model and pipeline.

        Args:
            data: Optional DataFrame with OHLC data. If None, uses loaded data.

        Returns:
            PredictionResult object with prediction details

        Raises:
            PredictorError: If prediction fails
        """
        try:
            if self.config.verbose:
                print("\n🎯 Making prediction...")
            self.logger.info("Starting prediction process")

            # Validate prerequisites
            if self.model is None or self.pipeline is None:
                raise PredictorError("Model and pipeline must be loaded before making predictions")

            # Get data
            if data is not None:
                # Use provided data
                prediction_data = data.copy()
            else:
                # Use loaded data
                prediction_data = self.get_data()

            # Filter out incomplete candle
            data_filtered, was_filtered = self.filter_incomplete_candle(prediction_data)

            # Create and transform features
            if self.config.verbose:
                print("\n🔧 Processing features...")
            self.logger.info("Processing features")

            feature_data = self.pipeline.create_base_features(data_filtered)
            X_pred = self.pipeline.transform(feature_data.iloc[[-1]])

            # Make prediction
            if self.config.verbose:
                print(f"🤖 Running prediction...")
            self.logger.info("Running model prediction")

            prediction_prob = self.model.predict(X_pred.values, verbose=0)[0][0]
            prediction_class = int(prediction_prob > 0.5)

            # Get info from the candle we're actually using for prediction
            prediction_candle = data_filtered.iloc[-1]
            prediction_candle_time = data_filtered.index[-1]

            # Get the latest price (from the incomplete candle if it exists)
            latest_price = prediction_data.iloc[-1]['close']
            latest_candle_time = prediction_data.index[-1]

            # Create result object
            result = PredictionResult(
                prediction_time=latest_candle_time.strftime("%Y-%m-%d %H:%M:%S"),
                symbol=self.symbol,
                prediction=prediction_class,
                probability=float(prediction_prob),
                prediction_based_on_candle=prediction_candle_time.strftime("%Y-%m-%d %H:%M:%S"),
                prediction_candle_close=float(prediction_candle['close']),
                latest_price=float(latest_price),
                latest_candle_time=latest_candle_time.strftime("%Y-%m-%d %H:%M:%S"),
                incomplete_candle_filtered=was_filtered
            )

            # Save results if configured
            if self.config.results_file:
                self.save_results(result)

            if self.config.verbose:
                print("✅ Prediction completed!")
            self.logger.info("Prediction completed successfully")

            return result

        except Exception as e:
            error_msg = f"Prediction failed: {str(e)}"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ Prediction error: {e}")

            # Return error result
            return PredictionResult(
                prediction_time="",
                symbol=self.symbol,
                prediction=0,
                probability=0.0,
                prediction_based_on_candle="",
                prediction_candle_close=0.0,
                latest_price=0.0,
                latest_candle_time="",
                incomplete_candle_filtered=False,
                error=error_msg
            )

    def save_results(self, result: PredictionResult) -> bool:
        """
        Save prediction results to file.

        Args:
            result: PredictionResult object to save

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            results_file = self.config.results_file

            # Convert result to dictionary for JSON serialization
            result_dict = {
                "prediction_time": result.prediction_time,
                "symbol": result.symbol,
                "prediction": result.prediction,
                "probability": result.probability,
                "prediction_based_on_candle": result.prediction_based_on_candle,
                "prediction_candle_close": result.prediction_candle_close,
                "latest_price": result.latest_price,
                "latest_candle_time": result.latest_candle_time,
                "incomplete_candle_filtered": result.incomplete_candle_filtered,
                "error": result.error,
                "warnings": result.warnings
            }

            # Load existing results or create new list
            if os.path.exists(results_file):
                with open(results_file, 'r') as f:
                    results = json.load(f)
            else:
                results = []

            # Add new result
            results.append(result_dict)

            # Save updated results
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)

            if self.config.verbose:
                print(f"✅ Results saved to {results_file}")
            self.logger.info(f"Results saved to {results_file}")

            return True

        except Exception as e:
            warning_msg = f"Could not save results: {str(e)}"
            self.logger.warning(warning_msg)
            if self.config.verbose:
                print(f"⚠️ Warning: {warning_msg}")
            return False

    def display_result(self, result: PredictionResult):
        """
        Display prediction result in a formatted way.

        Args:
            result: PredictionResult object to display
        """
        if result.error:
            print(f"\n❌ ERROR: {result.error}")
            return

        print(f"\n{'='*60}")
        print(f"🎯 PREDICTION RESULT")
        print(f"{'='*60}")

        print(f"📈 Symbol: {result.symbol}")
        print(f"💰 Latest Price: {result.latest_price:.5f}")
        print(f"📅 Latest Candle: {result.latest_candle_time}")

        if result.incomplete_candle_filtered:
            print(f"\n⚠️  INCOMPLETE CANDLE DETECTED AND FILTERED")
            print(f"📊 Prediction based on: {result.prediction_based_on_candle}")
            print(f"💰 Candle close used: {result.prediction_candle_close:.5f}")

        print(f"\n🕐 Prediction Time: {result.prediction_time}")
        print(f"\n🤖 PREDICTION: {'BOUNCE' if result.prediction == 1 else 'NO BOUNCE'}")
        print(f"📊 Probability: {result.probability:.4f} ({result.probability*100:.2f}%)")

        # Add confidence interpretation
        if result.probability > 0.7:
            confidence = "HIGH"
            emoji = "🟢"
        elif result.probability > 0.6:
            confidence = "MODERATE"
            emoji = "🟡"
        else:
            confidence = "LOW"
            emoji = "🔴"

        print(f"{emoji} Confidence: {confidence}")

        if result.warnings:
            print(f"\n⚠️ Warnings:")
            for warning in result.warnings:
                print(f"   • {warning}")

        print(f"{'='*60}")

    def run(self) -> PredictionResult:
        """
        Run the complete prediction pipeline (for script execution).

        Returns:
            PredictionResult object
        """
        if self.config.verbose:
            print("="*60)
            print("🚀 FIBONACCI BOUNCE PREDICTOR")
            print("="*60)

        try:
            # Load model and pipeline
            self.load_model_and_pipeline()

            # Load data from CSV
            self.load_data()

            # Make prediction
            result = self.make_prediction()

            # Display result if verbose
            if self.config.verbose:
                self.display_result(result)

            return result

        except Exception as e:
            error_msg = f"Prediction pipeline failed: {str(e)}"
            self.logger.error(error_msg)
            if self.config.verbose:
                print(f"❌ Error: {e}")

            return PredictionResult(
                prediction_time="",
                symbol=self.symbol,
                prediction=0,
                probability=0.0,
                prediction_based_on_candle="",
                prediction_candle_close=0.0,
                latest_price=0.0,
                latest_candle_time="",
                incomplete_candle_filtered=False,
                error=error_msg
            )

    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model and pipeline.

        Returns:
            Dictionary with model information
        """
        info = {
            "model_loaded": self.model is not None,
            "pipeline_loaded": self.pipeline is not None,
            "features_loaded": self.selected_features is not None,
            "data_loaded": self.data is not None,
            "symbol": self.symbol,
            "config": {
                "model_path": self.config.model_path,
                "pipeline_path": self.config.pipeline_path,
                "features_path": self.config.features_path,
                "data_file": self.config.data_file,
                "verbose": self.config.verbose
            }
        }

        if self.selected_features:
            info["num_features"] = len(self.selected_features)

        if self.data is not None:
            info["data_shape"] = self.data.shape
            info["data_columns"] = list(self.data.columns)
            info["data_date_range"] = {
                "start": self.data.index[0].strftime("%Y-%m-%d %H:%M:%S"),
                "end": self.data.index[-1].strftime("%Y-%m-%d %H:%M:%S")
            }

        if self.pipeline and hasattr(self.pipeline, '_feature_creation_stats'):
            info["feature_stats"] = self.pipeline.get_feature_stats()

        return info

# Public API Functions

def load_predictor(config: Optional[PredictorConfig] = None) -> FibonacciBouncePredictor:
    """
    Load and initialize a Fibonacci Bounce Predictor.

    Args:
        config: Optional configuration object. If None, uses default configuration.

    Returns:
        Initialized FibonacciBouncePredictor instance

    Raises:
        ModelLoadError: If model loading fails

    Example:
        >>> config = PredictorConfig(model_path="my_model.keras", verbose=False)
        >>> predictor = load_predictor(config)
        >>> result = predictor.make_prediction()
    """
    predictor = FibonacciBouncePredictor(config)
    predictor.load_model_and_pipeline()
    return predictor


def predict_from_data(data: pd.DataFrame,
                     config: Optional[PredictorConfig] = None,
                     symbol: Optional[str] = None) -> PredictionResult:
    """
    Make a prediction from pandas DataFrame data.

    Args:
        data: DataFrame with OHLC data (must have 'time', 'open', 'high', 'low', 'close' columns)
        config: Optional configuration object
        symbol: Optional symbol name

    Returns:
        PredictionResult object

    Example:
        >>> import pandas as pd
        >>> data = pd.read_csv("EURJPY_data.csv")
        >>> result = predict_from_data(data, symbol="EURJPY")
        >>> print(f"Prediction: {result.prediction}, Probability: {result.probability}")
    """
    predictor = load_predictor(config)
    predictor.load_data_from_dataframe(data, symbol)
    return predictor.make_prediction()


def predict_from_file(filepath: str,
                     config: Optional[PredictorConfig] = None) -> PredictionResult:
    """
    Make a prediction from a CSV file.

    Args:
        filepath: Path to CSV file with OHLC data
        config: Optional configuration object

    Returns:
        PredictionResult object

    Example:
        >>> result = predict_from_file("EURJPY_data.csv")
        >>> print(f"Prediction: {result.prediction}, Probability: {result.probability}")
    """
    if config is None:
        config = PredictorConfig()
    config.data_file = filepath

    predictor = load_predictor(config)
    predictor.load_data()
    return predictor.make_prediction()


def batch_predict(data_list: List[pd.DataFrame],
                 config: Optional[PredictorConfig] = None,
                 symbols: Optional[List[str]] = None) -> List[PredictionResult]:
    """
    Make predictions for multiple datasets.

    Args:
        data_list: List of DataFrames with OHLC data
        config: Optional configuration object
        symbols: Optional list of symbol names (must match data_list length)

    Returns:
        List of PredictionResult objects

    Example:
        >>> data_list = [df1, df2, df3]
        >>> symbols = ["EURJPY", "GBPUSD", "USDJPY"]
        >>> results = batch_predict(data_list, symbols=symbols)
        >>> for result in results:
        ...     print(f"{result.symbol}: {result.prediction}")
    """
    if symbols and len(symbols) != len(data_list):
        raise ValueError("symbols list must match data_list length")

    predictor = load_predictor(config)
    results = []

    for i, data in enumerate(data_list):
        symbol = symbols[i] if symbols else f"SYMBOL_{i+1}"
        try:
            predictor.load_data_from_dataframe(data, symbol)
            result = predictor.make_prediction()
            results.append(result)
        except Exception as e:
            error_result = PredictionResult(
                prediction_time="",
                symbol=symbol,
                prediction=0,
                probability=0.0,
                prediction_based_on_candle="",
                prediction_candle_close=0.0,
                latest_price=0.0,
                latest_candle_time="",
                incomplete_candle_filtered=False,
                error=f"Batch prediction failed: {str(e)}"
            )
            results.append(error_result)

    return results


def check_dependencies(verbose: bool = True) -> bool:
    """
    Check if all required dependencies are installed.

    Args:
        verbose: Whether to print dependency status

    Returns:
        True if all dependencies are available, False otherwise
    """
    if verbose:
        print("🔍 Checking dependencies...")

    missing_deps = []

    # Check core dependencies
    required_deps = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('tensorflow', 'tensorflow')
    ]

    for dep_name, import_name in required_deps:
        try:
            __import__(import_name)
            if verbose:
                print(f"✅ {dep_name} - OK")
        except ImportError:
            if verbose:
                print(f"❌ {dep_name} - Missing")
            missing_deps.append(dep_name)

    if missing_deps:
        if verbose:
            print(f"\n📦 Missing dependencies: {', '.join(missing_deps)}")
            print("\n🔧 To install missing dependencies:")
            for dep in missing_deps:
                print(f"   pip install {dep}")
        return False

    if verbose:
        print("✅ All dependencies are installed!")
    return True


def get_model_info(config: Optional[PredictorConfig] = None) -> Dict[str, Any]:
    """
    Get information about the model and its requirements.

    Args:
        config: Optional configuration object

    Returns:
        Dictionary with model information
    """
    try:
        predictor = load_predictor(config)
        return predictor.get_model_info()
    except Exception as e:
        return {"error": f"Failed to get model info: {str(e)}"}


# Main execution block
if __name__ == "__main__":
    if check_dependencies():
        predictor = FibonacciBouncePredictor()
        result = predictor.run()

        # Exit with appropriate code
        if result.error:
            sys.exit(1)
        else:
            sys.exit(0)
    else:
        print("❌ Please install missing dependencies first")
        sys.exit(1)


