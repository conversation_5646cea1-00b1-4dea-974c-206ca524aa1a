"""
In the follwing code when retriving the raw dat we filter out the latest raw because its still forming
and its OHLC dat cant be used by the model so we remain with data only up to the latest 2nd raw
"""
import os
import sys
import warnings
import pandas as pd
import numpy as np
import pickle
import json
import tensorflow as tf
from datetime import datetime, timedelta
import glob

# Suppress warnings and TensorFlow logs
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class SymbolicFeatureGenerator:
    def __init__(self):
        self.feature_recipes = []
        self.new_feature_names = []

    def _apply_operation(self, recipe, x, y=None, cond=None):
        try:
            operation = eval(recipe['operation_str'])
            if recipe.get('is_ternary', False):
                return operation(cond, x, y)
            elif recipe['is_binary']:
                return operation(x, y)
            else:
                return operation(x, None)
        except Exception as e:
            print(f"⚠️ Warning: Error in operation {recipe.get('name', 'unknown')}: {e}")
            return np.full_like(x, np.nan) if hasattr(x, '__len__') else np.nan

    def transform(self, X):
        X_copy = X.copy()
        for recipe in self.feature_recipes:
            try:
                if recipe.get('is_ternary', False):
                    required = [recipe['condition'], recipe['feature1'], recipe['feature2']]
                    if all(f in X_copy.columns for f in required):
                        X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']], X_copy[recipe['feature2']], X_copy[recipe['condition']])
                    else:
                        missing = [f for f in required if f not in X_copy.columns]
                        print(f"⚠️ Warning: Missing features {missing} for {recipe['name']}")
                        X_copy[recipe['name']] = np.nan
                elif recipe['is_binary']:
                    required = [recipe['feature1'], recipe['feature2']]
                    if all(f in X_copy.columns for f in required):
                        X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']], X_copy[recipe['feature2']])
                    else:
                        missing = [f for f in required if f not in X_copy.columns]
                        print(f"⚠️ Warning: Missing features {missing} for {recipe['name']}")
                        X_copy[recipe['name']] = np.nan
                else:
                    if recipe['feature1'] in X_copy.columns:
                        X_copy[recipe['name']] = self._apply_operation(recipe, X_copy[recipe['feature1']])
                    else:
                        print(f"⚠️ Warning: Missing feature {recipe['feature1']} for {recipe['name']}")
                        X_copy[recipe['name']] = np.nan
            except Exception as e:
                print(f"⚠️ Warning: Error computing {recipe['name']}: {e}")
                X_copy[recipe['name']] = np.nan
        return X_copy.replace([np.inf, -np.inf], np.nan)

class FeatureEngineeringPipeline:
    def __init__(self):
        self.scaler = None
        self.selected_features = []
        self.symbolic_generator = None
        self.pipeline_config = {}

    def create_base_features(self, df):
        print("🔧 Creating base features...")
        data = df.copy()
        
        # Check for required columns
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Check for NaN values in raw data
        if data.isna().any().any():
            print("⚠️ Warning: NaN values detected in raw data")
            data = data.fillna(method='ffill').fillna(method='bfill')
        
        # Swing features
        for period in [20, 50, 100]:
            data[f'swing_high_{period}'] = data['high'].rolling(period, min_periods=1).max()
            data[f'swing_low_{period}'] = data['low'].rolling(period, min_periods=1).min()
            data[f'swing_range_{period}'] = data[f'swing_high_{period}'] - data[f'swing_low_{period}']
            
            # Fibonacci levels
            for level in [0.236, 0.382, 0.5, 0.618, 0.786]:
                level_str = int(level * 1000)
                data[f'fib_{level_str}_{period}'] = data[f'swing_high_{period}'] - level * data[f'swing_range_{period}']
                data[f'distance_to_fib_{level_str}_{period}'] = (data['close'] - data[f'fib_{level_str}_{period}']) / data['close']
                data[f'abs_distance_to_fib_{level_str}_{period}'] = abs(data[f'distance_to_fib_{level_str}_{period}'])

        # Support/Resistance Features - Fib Touches
        for period in [20, 50]:
            for level in [0.382, 0.618]:
                level_str = int(level * 1000)
                col_name = f'fib_touches_{level_str}_{period}'
                data[col_name] = data[f'abs_distance_to_fib_{level_str}_{period}'].rolling(period, min_periods=1).apply(
                    lambda x: (x < 0.0005).sum(), raw=False
                )

        # Confluence score
        data['fib_confluence_score'] = 0
        for period in [20, 50]:
            for level in [0.382, 0.5, 0.618]:
                level_str = int(level * 1000)
                data['fib_confluence_score'] += (data[f'abs_distance_to_fib_{level_str}_{period}'] < 0.001).astype(int)

        # Trend and momentum
        data['trend_strength_10'] = (data['close'] - data['close'].shift(10)) / data['close'].shift(10)
        data['trend_strength_20'] = (data['close'] - data['close'].shift(20)) / data['close'].shift(20)
        
        # Moving averages
        for period in [10, 20, 50]:
            data[f'sma_{period}'] = data['close'].rolling(period, min_periods=1).mean()
            data[f'above_sma_{period}'] = (data['close'] > data[f'sma_{period}']).astype(int)
        
        data['sma_alignment_bullish'] = ((data['sma_10'] > data['sma_20']) & (data['sma_20'] > data['sma_50'])).astype(int)
        data['sma_alignment_bearish'] = ((data['sma_10'] < data['sma_20']) & (data['sma_20'] < data['sma_50'])).astype(int)

        # Momentum
        for period in [3, 5, 10]:
            data[f'momentum_{period}'] = data['close'] - data['close'].shift(period)
        data['momentum_decreasing'] = (abs(data['momentum_3']) < abs(data['momentum_5'])).astype(int)

        # Volatility
        data['atr_14'] = (data['high'] - data['low']).rolling(14, min_periods=1).mean()
        data['current_range'] = data['high'] - data['low']
        data['volatility_ratio'] = data['current_range'] / data['atr_14']
        data['recent_volatility_expansion'] = (data['volatility_ratio'].rolling(3, min_periods=1).max() > 1.5).astype(int)

        # Candlestick patterns
        data['body_size'] = abs(data['close'] - data['open'])
        data['upper_shadow'] = data['high'] - data[['open', 'close']].max(axis=1)
        data['lower_shadow'] = data[['open', 'close']].min(axis=1) - data['low']
        data['total_range'] = data['high'] - data['low']
        data['hammer_pattern'] = ((data['lower_shadow'] > data['body_size'] * 2) & (data['upper_shadow'] < data['body_size'] * 0.5)).astype(int)
        data['doji_pattern'] = (data['body_size'] / data['total_range'] < 0.1).astype(int)

        # Trading sessions
        data['hour'] = data.index.hour
        data['london_session'] = data['hour'].between(8, 16).astype(int)
        data['ny_session'] = data['hour'].between(13, 21).astype(int)
        data['session_overlap'] = (data['london_session'] & data['ny_session']).astype(int)

        # Volume features
        if 'volume' in data.columns:
            data['volume_ma_20'] = data['volume'].rolling(20, min_periods=1).mean()
            data['volume_ratio'] = data['volume'] / data['volume_ma_20']
            data['volume_confirmation'] = (data['volume_ratio'] > 1.2).astype(int)
        else:
            print("⚠️ Warning: 'volume' column missing, skipping volume features")

        # Replace inf with NaN and handle missing values
        data = data.replace([np.inf, -np.inf], np.nan)
        data = data.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        return data

    def transform(self, X):
        if self.scaler is None:
            raise ValueError("Pipeline not fitted. Load the pipeline first.")
        
        feature_columns_to_exclude = ['target', 'next_fib_bounce', 'future_open', 'timestamp', 'timestamp_original']
        
        if 'feature_columns_used_for_scaling' in self.pipeline_config:
            feature_cols = [col for col in self.pipeline_config['feature_columns_used_for_scaling'] if col in X.columns]
        else:
            feature_cols = [col for col in X.columns if col not in feature_columns_to_exclude and X[col].dtype in [np.float64, np.int64]]

        X_scaled = pd.DataFrame(self.scaler.transform(X[feature_cols]), columns=feature_cols, index=X.index)
        X_with_symbolic = self.symbolic_generator.transform(X_scaled)

        # Check for missing features
        missing_features = [f for f in self.selected_features if f not in X_with_symbolic.columns]
        if missing_features:
            raise ValueError(f"Missing features: {missing_features}")

        X_final = X_with_symbolic[self.selected_features]
        
        # Handle any remaining NaN values
        if X_final.isna().any().any():
            print("⚠️ Warning: NaN values found in final features, filling with 0")
            X_final = X_final.fillna(0)

        return X_final

    @classmethod
    def load(cls, filepath):
        print(f"📥 Loading pipeline from {filepath}")
        with open(filepath, 'rb') as f:
            pipeline_data = pickle.load(f)

        pipeline = cls()
        pipeline.scaler = pipeline_data['scaler']
        pipeline.selected_features = pipeline_data['selected_features']
        pipeline.symbolic_generator = pipeline_data['symbolic_generator']
        pipeline.pipeline_config = pipeline_data['pipeline_config']
        print(f"✅ Pipeline loaded with {len(pipeline.selected_features)} features")
        return pipeline

class FibonacciBouncePredictor:
    def __init__(self):
        self.model = None
        self.pipeline = None
        self.selected_features = None
        self.symbol = "EURJPY"
        self.data_file = None
        
        # Use specific file names
        self.model_path = "final_dl_model.keras"
        self.pipeline_path = "feature_engineering_pipeline.pkl"
        self.features_path = "selected_features.json"
        
        self._check_files()

    def _check_files(self):
        """Check if required files exist"""
        current_dir = os.getcwd()
        
        # Check for model file
        if not os.path.exists(self.model_path):
            print(f"❌ Model file not found: {self.model_path}")
        
        # Check for pipeline file
        if not os.path.exists(self.pipeline_path):
            print(f"❌ Pipeline file not found: {self.pipeline_path}")
        
        # Check for features file
        if not os.path.exists(self.features_path):
            print(f"❌ Features file not found: {self.features_path}")
        
        # Find CSV data file
        csv_files = glob.glob(os.path.join(current_dir, '*.csv'))
        if csv_files:
            self.data_file = csv_files[0]
            print(f"✅ Found data file: {os.path.basename(self.data_file)}")
        else:
            print("❌ No CSV data file found")

    def load_model_and_pipeline(self):
        try:
            print("\n🚀 Loading model and pipeline...")
            
            # Load model
            if not os.path.exists(self.model_path):
                print(f"❌ Model file not found: {self.model_path}")
                return False
            
            self.model = tf.keras.models.load_model(self.model_path)
            print(f"✅ Model loaded: {self.model_path}")
            
            # Load pipeline
            if not os.path.exists(self.pipeline_path):
                print(f"❌ Pipeline file not found: {self.pipeline_path}")
                return False
            
            self.pipeline = FeatureEngineeringPipeline.load(self.pipeline_path)
            print(f"✅ Pipeline loaded: {self.pipeline_path}")
            
            # Load features
            if os.path.exists(self.features_path):
                with open(self.features_path, 'r') as f:
                    self.selected_features = json.load(f)
                self.pipeline.selected_features = self.selected_features
                print(f"✅ Features loaded: {len(self.selected_features)} features")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading model and pipeline: {e}")
            return False

    def load_data(self):
        """Load data from CSV file with proper volume handling"""
        if not self.data_file or not os.path.exists(self.data_file):
            print(f"❌ Data file not found: {self.data_file}")
            return False
        
        print(f"📊 Loading data from: {os.path.basename(self.data_file)}")
        
        try:
            # Load CSV data
            df = pd.read_csv(self.data_file)
            print(f"✅ Data loaded: {len(df)} rows")
            
            # Check required columns
            required_cols = ['time', 'open', 'high', 'low', 'close']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"❌ Missing required columns: {missing_cols}")
                return False
            
            # Convert time to datetime and set as index
            df['time'] = pd.to_datetime(df['time'])
            df = df.set_index('time')
            
            # Handle volume columns
            if 'tick_volume' in df.columns:
                df['volume'] = df['tick_volume']
            elif 'real_volume' in df.columns:
                df['volume'] = df['real_volume']
            
            # Keep only OHLCV columns
            columns_to_keep = ['open', 'high', 'low', 'close']
            if 'volume' in df.columns:
                columns_to_keep.append('volume')
            
            df = df[columns_to_keep]
            
            # Handle any missing values
            if df.isna().any().any():
                df = df.fillna(method='ffill').fillna(method='bfill')
            
            # Extract symbol from filename if possible
            filename = os.path.basename(self.data_file)
            if '_' in filename:
                self.symbol = filename.split('_')[0]
            
            self.data = df
            print(f"✅ Data processed: {len(df)} candles")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False

    def get_data(self, count=None):
        """Get data from loaded CSV file"""
        if not hasattr(self, 'data') or self.data is None:
            print("❌ No data loaded")
            return None
        
        try:
            if count is not None:
                data = self.data.tail(count).copy()
            else:
                data = self.data.copy()
            
            return data
            
        except Exception as e:
            print(f"❌ Error getting data: {e}")
            return None

    def filter_incomplete_candle(self, data):
        """
        Filter out the incomplete current candle and use the last complete candle
        """
        # Get the latest time from the raw data (this is the system time)
        latest_data_time = data.index[-1]
        
        # Get the last candle time
        last_candle_time = data.index[-1]
        
        # Calculate candle interval (assumes uniform intervals)
        if len(data) >= 2:
            candle_interval = data.index[-1] - data.index[-2]
        else:
            # Default to 5 minutes if we can't determine
            candle_interval = timedelta(minutes=5)
        
        # Expected completion time for the last candle
        expected_completion = last_candle_time + candle_interval
        
        print(f"\n🕐 Current time: {latest_data_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 Last candle time: {last_candle_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Expected completion: {expected_completion.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # For this logic, we assume the latest candle is incomplete and filter it out
        # This is because the latest candle in the data is still being formed
        print(f"⚠️  Last candle is assumed INCOMPLETE (current candle still forming)")
        print(f"✅ Using previous complete candle for prediction")
        
        # Remove the last (incomplete) candle and use the second-to-last
        data_filtered = data[:-1].copy()
        used_candle_time = data_filtered.index[-1]
        print(f"📍 Using candle from: {used_candle_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return data_filtered, True

    def make_prediction(self):
        try:
            print("\n🎯 Making prediction...")
            
            # Get data
            data = self.get_data()
            if data is None:
                return {"error": "Failed to get market data"}
            
            # Filter out incomplete candle
            data_filtered, was_filtered = self.filter_incomplete_candle(data)
            
            # Create and transform features
            print("\n🔧 Processing features...")
            feature_data = self.pipeline.create_base_features(data_filtered)
            X_pred = self.pipeline.transform(feature_data.iloc[[-1]])
            
            # Make prediction
            print(f"🤖 Running prediction...")
            prediction_prob = self.model.predict(X_pred.values, verbose=0)[0][0]
            prediction_class = int(prediction_prob > 0.5)
            
            # Get info from the candle we're actually using for prediction
            prediction_candle = data_filtered.iloc[-1]
            prediction_candle_time = data_filtered.index[-1]
            
            # Get the latest price (from the incomplete candle if it exists)
            latest_price = data.iloc[-1]['close']
            latest_candle_time = data.index[-1]
            
            result = {
                "prediction_time": latest_candle_time.strftime("%Y-%m-%d %H:%M:%S"),  # Use latest time from data
                "symbol": self.symbol,
                "prediction": prediction_class,
                "probability": float(prediction_prob),
                "prediction_based_on_candle": prediction_candle_time.strftime("%Y-%m-%d %H:%M:%S"),
                "prediction_candle_close": float(prediction_candle['close']),
                "latest_price": float(latest_price),
                "latest_candle_time": latest_candle_time.strftime("%Y-%m-%d %H:%M:%S"),
                "incomplete_candle_filtered": was_filtered
            }
            
            # Save results
            self.save_results(result)
            
            print("✅ Prediction completed!")
            return result
            
        except Exception as e:
            print(f"❌ Prediction error: {e}")
            return {"error": f"Prediction failed: {str(e)}"}

    def save_results(self, result):
        """Save prediction results to file"""
        try:
            results_file = "prediction_results.json"
            
            # Load existing results or create new list
            if os.path.exists(results_file):
                with open(results_file, 'r') as f:
                    results = json.load(f)
            else:
                results = []
            
            # Add new result
            results.append(result)
            
            # Save updated results
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"✅ Results saved to {results_file}")
            
        except Exception as e:
            print(f"⚠️ Warning: Could not save results: {e}")

    def display_result(self, result):
        if "error" in result:
            print(f"\n❌ ERROR: {result['error']}")
            return

        print(f"\n{'='*60}")
        print(f"🎯 PREDICTION RESULT")
        print(f"{'='*60}")
        
        print(f"📈 Symbol: {result['symbol']}")
        print(f"💰 Latest Price: {result['latest_price']:.5f}")
        print(f"📅 Latest Candle: {result['latest_candle_time']}")
        
        if result['incomplete_candle_filtered']:
            print(f"\n⚠️  INCOMPLETE CANDLE DETECTED AND FILTERED")
            print(f"📊 Prediction based on: {result['prediction_based_on_candle']}")
            print(f"💰 Candle close used: {result['prediction_candle_close']:.5f}")
        
        print(f"\n🕐 Prediction Time: {result['prediction_time']}")
        print(f"\n🤖 PREDICTION: {'BOUNCE' if result['prediction'] == 1 else 'NO BOUNCE'}")
        print(f"📊 Probability: {result['probability']:.4f} ({result['probability']*100:.2f}%)")
        
        # Add confidence interpretation
        if result['probability'] > 0.7:
            confidence = "HIGH"
            emoji = "🟢"
        elif result['probability'] > 0.6:
            confidence = "MODERATE"
            emoji = "🟡"
        else:
            confidence = "LOW"
            emoji = "🔴"
        
        print(f"{emoji} Confidence: {confidence}")
        
        print(f"{'='*60}")

    def run(self):
        print("="*60)
        print("🚀 FIBONACCI BOUNCE PREDICTOR")
        print("="*60)
        
        try:
            # Load model and pipeline
            if not self.load_model_and_pipeline():
                print("❌ Failed to load model/pipeline")
                return
            
            # Load data from CSV
            if not self.load_data():
                print("❌ Failed to load data")
                return
            
            # Make prediction
            result = self.make_prediction()
            self.display_result(result)
            
        except Exception as e:
            print(f"❌ Error: {e}")

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    missing_deps = []
    
    # Check core dependencies
    required_deps = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('tensorflow', 'tensorflow')
    ]
    
    for dep_name, import_name in required_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} - OK")
        except ImportError:
            print(f"❌ {dep_name} - Missing")
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"\n📦 Missing dependencies: {', '.join(missing_deps)}")
        print("\n🔧 To install missing dependencies:")
        for dep in missing_deps:
            print(f"   pip install {dep}")
        return False
    
    print("✅ All dependencies are installed!")
    return True

if __name__ == "__main__":
    if check_dependencies():
        predictor = FibonacciBouncePredictor()
        predictor.run()
    else:
        print("❌ Please install missing dependencies first")


