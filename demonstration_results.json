{"scenario_1": {"success": false, "error": "2025-07-30 22:22:37.317520: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-07-30 22:22:40.347101: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\nTraceback (most recent call last):\n  File \"F:\\production_2\\predictions_5.py\", line 1432, in <module>\n    if check_dependencies():\n       ^^^^^^^^^^^^^^^^^^^^\n  File \"F:\\production_2\\predictions_5.py\", line 1379, in check_dependencies\n    print(\"\\U0001f50d Checking dependencies...\")\n  File \"C:\\Users\\<USER>\\anaconda3\\Lib\\encodings\\cp1252.py\", line 19, in encode\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f50d' in position 0: character maps to <undefined>\n"}, "scenario_2": {"success": false, "error": "Failed to load pipeline from feature_engineering_pipeline.pkl: Can't get attribute 'SymbolicFeatureGenerator' on <module '__main__' from 'F:\\\\production_2\\\\comprehensive_demonstration.py'>"}, "scenario_3": {"success": false, "error": "Failed to load pipeline from feature_engineering_pipeline.pkl: Can't get attribute 'SymbolicFeatureGenerator' on <module '__main__' from 'F:\\\\production_2\\\\comprehensive_demonstration.py'>"}, "scenario_4": {"success": false, "error": "Failed to load pipeline from feature_engineering_pipeline.pkl: Can't get attribute 'SymbolicFeatureGenerator' on <module '__main__' from 'F:\\\\production_2\\\\comprehensive_demonstration.py'>"}, "scenario_5": {"success": false, "error": "Failed to load pipeline from feature_engineering_pipeline.pkl: Can't get attribute 'SymbolicFeatureGenerator' on <module '__main__' from 'F:\\\\production_2\\\\comprehensive_demonstration.py'>"}, "scenario_6": {"success": true, "tests_passed": 3, "total_tests": 4}, "scenario_7": {"success": false, "error": "Failed to load pipeline from feature_engineering_pipeline.pkl: Can't get attribute 'SymbolicFeatureGenerator' on <module '__main__' from 'F:\\\\production_2\\\\comprehensive_demonstration.py'>"}}