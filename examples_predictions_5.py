"""
Comprehensive usage examples for the refactored predictions_5.py module.

This file demonstrates various ways to use the refactored Fibonacci Bounce Predictor
module, including basic usage, advanced configuration, error handling, and batch processing.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# Import the refactored module components
from predictions_5 import (
    FibonacciBouncePredictor,
    PredictorConfig,
    PredictionResult,
    load_predictor,
    predict_from_data,
    predict_from_file,
    batch_predict,
    check_dependencies,
    get_model_info,
    ModelLoadError,
    DataError,
    FeatureError
)


def example_1_basic_usage():
    """Example 1: Basic usage with default configuration"""
    print("="*60)
    print("EXAMPLE 1: Basic Usage")
    print("="*60)
    
    try:
        # Check if dependencies are available
        if not check_dependencies(verbose=True):
            print("❌ Dependencies not available")
            return
        
        # Basic usage - load predictor with default config
        print("\n🚀 Loading predictor with default configuration...")
        predictor = load_predictor()
        
        # Get model information
        info = predictor.get_model_info()
        print(f"✅ Model loaded: {info['model_loaded']}")
        print(f"✅ Pipeline loaded: {info['pipeline_loaded']}")
        
        # Make prediction (will use auto-discovered CSV file)
        result = predictor.make_prediction()
        
        # Display results
        print(f"\n📊 Prediction Results:")
        print(f"   Symbol: {result.symbol}")
        print(f"   Prediction: {'BOUNCE' if result.prediction == 1 else 'NO BOUNCE'}")
        print(f"   Probability: {result.probability:.4f}")
        print(f"   Latest Price: {result.latest_price:.5f}")
        
    except Exception as e:
        print(f"❌ Error in basic usage example: {e}")


def example_2_custom_configuration():
    """Example 2: Custom configuration and file paths"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Custom Configuration")
    print("="*60)
    
    try:
        # Create custom configuration
        config = PredictorConfig(
            model_path="final_dl_model.keras",  # Custom model path
            pipeline_path="feature_engineering_pipeline.pkl",  # Custom pipeline path
            features_path="selected_features.json",  # Custom features path
            data_file="EURJPY_M5_data_filtered.csv",  # Specific data file
            symbol="EURJPY",  # Custom symbol
            verbose=True,  # Enable verbose output
            auto_find_data=False,  # Disable auto data discovery
            logging_level="INFO"  # Set logging level
        )
        
        print(f"📋 Configuration:")
        print(f"   Model: {config.model_path}")
        print(f"   Data: {config.data_file}")
        print(f"   Symbol: {config.symbol}")
        print(f"   Verbose: {config.verbose}")
        
        # Load predictor with custom config
        predictor = load_predictor(config)
        
        # Make prediction
        result = predictor.make_prediction()
        
        print(f"\n📊 Custom Configuration Results:")
        print(f"   Symbol: {result.symbol}")
        print(f"   Prediction: {result.prediction}")
        print(f"   Probability: {result.probability:.4f}")
        
    except Exception as e:
        print(f"❌ Error in custom configuration example: {e}")


def example_3_dataframe_input():
    """Example 3: Using DataFrame input instead of CSV files"""
    print("\n" + "="*60)
    print("EXAMPLE 3: DataFrame Input")
    print("="*60)
    
    try:
        # Create sample OHLC data
        print("📊 Creating sample OHLC data...")
        dates = pd.date_range(start='2024-01-01', periods=500, freq='5T')
        
        # Generate realistic price data
        np.random.seed(42)
        base_price = 150.0
        data = []
        current_price = base_price
        
        for date in dates:
            # Random walk
            change = np.random.normal(0, 0.001) * current_price
            current_price += change
            
            # Generate OHLC
            volatility = np.random.uniform(0.0005, 0.002) * current_price
            open_price = current_price + np.random.uniform(-volatility/2, volatility/2)
            high_price = max(open_price, current_price) + np.random.uniform(0, volatility)
            low_price = min(open_price, current_price) - np.random.uniform(0, volatility)
            close_price = current_price
            
            data.append({
                'time': date,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': np.random.randint(100, 1000)
            })
        
        df = pd.DataFrame(data)
        print(f"✅ Created sample data: {len(df)} rows")
        
        # Use the convenience function for DataFrame prediction
        result = predict_from_data(
            data=df,
            config=PredictorConfig(verbose=False),
            symbol="TESTPAIR"
        )
        
        print(f"\n📊 DataFrame Input Results:")
        print(f"   Symbol: {result.symbol}")
        print(f"   Data Points: {len(df)}")
        print(f"   Prediction: {result.prediction}")
        print(f"   Probability: {result.probability:.4f}")
        
    except Exception as e:
        print(f"❌ Error in DataFrame input example: {e}")


def example_4_batch_processing():
    """Example 4: Batch processing multiple datasets"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Batch Processing")
    print("="*60)
    
    try:
        # Create multiple sample datasets
        print("📊 Creating multiple sample datasets...")
        
        datasets = []
        symbols = ["EURJPY", "GBPUSD", "USDJPY"]
        
        for i, symbol in enumerate(symbols):
            # Create unique data for each symbol
            dates = pd.date_range(start='2024-01-01', periods=300, freq='5T')
            np.random.seed(42 + i)  # Different seed for each dataset
            
            base_price = 150.0 + i * 10  # Different base price
            data = []
            current_price = base_price
            
            for date in dates:
                change = np.random.normal(0, 0.001) * current_price
                current_price += change
                
                volatility = np.random.uniform(0.0005, 0.002) * current_price
                open_price = current_price + np.random.uniform(-volatility/2, volatility/2)
                high_price = max(open_price, current_price) + np.random.uniform(0, volatility)
                low_price = min(open_price, current_price) - np.random.uniform(0, volatility)
                close_price = current_price
                
                data.append({
                    'time': date,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': np.random.randint(100, 1000)
                })
            
            datasets.append(pd.DataFrame(data))
        
        print(f"✅ Created {len(datasets)} datasets")
        
        # Batch process all datasets
        print("\n🔄 Processing batch predictions...")
        results = batch_predict(
            data_list=datasets,
            config=PredictorConfig(verbose=False),
            symbols=symbols
        )
        
        print(f"\n📊 Batch Processing Results:")
        for result in results:
            if result.error:
                print(f"   ❌ {result.symbol}: Error - {result.error}")
            else:
                prediction_text = "BOUNCE" if result.prediction == 1 else "NO BOUNCE"
                print(f"   ✅ {result.symbol}: {prediction_text} (prob: {result.probability:.4f})")
        
    except Exception as e:
        print(f"❌ Error in batch processing example: {e}")


def example_5_error_handling():
    """Example 5: Comprehensive error handling"""
    print("\n" + "="*60)
    print("EXAMPLE 5: Error Handling")
    print("="*60)
    
    # Test 1: Missing model file
    print("\n🧪 Test 1: Missing model file")
    try:
        config = PredictorConfig(model_path="nonexistent_model.keras", verbose=False)
        predictor = load_predictor(config)
    except ModelLoadError as e:
        print(f"   ✅ Correctly caught ModelLoadError: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # Test 2: Invalid data format
    print("\n🧪 Test 2: Invalid data format")
    try:
        invalid_data = pd.DataFrame({'invalid_column': [1, 2, 3]})
        result = predict_from_data(invalid_data, config=PredictorConfig(verbose=False))
    except DataError as e:
        print(f"   ✅ Correctly caught DataError: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # Test 3: Non-existent file
    print("\n🧪 Test 3: Non-existent file")
    try:
        result = predict_from_file("nonexistent_file.csv", config=PredictorConfig(verbose=False))
    except DataError as e:
        print(f"   ✅ Correctly caught DataError: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    print("\n✅ Error handling tests completed")


def example_6_model_information():
    """Example 6: Getting model and system information"""
    print("\n" + "="*60)
    print("EXAMPLE 6: Model Information")
    print("="*60)
    
    try:
        # Get model info without loading (safe operation)
        print("📋 Getting model information...")
        info = get_model_info(PredictorConfig(verbose=False))
        
        print(f"\n📊 Model Information:")
        for key, value in info.items():
            if key == 'config':
                print(f"   {key}:")
                for config_key, config_value in value.items():
                    print(f"     {config_key}: {config_value}")
            else:
                print(f"   {key}: {value}")
        
        # Check dependencies
        print(f"\n🔍 Dependency Check:")
        deps_ok = check_dependencies(verbose=False)
        print(f"   All dependencies available: {deps_ok}")
        
    except Exception as e:
        print(f"❌ Error getting model information: {e}")


def example_7_direct_script_compatibility():
    """Example 7: Demonstrating direct script execution compatibility"""
    print("\n" + "="*60)
    print("EXAMPLE 7: Direct Script Compatibility")
    print("="*60)
    
    print("📝 The refactored module maintains full backward compatibility.")
    print("   You can still run the script directly:")
    print("   ")
    print("   python predictions_5.py")
    print("   ")
    print("   This will execute the same logic as the original script,")
    print("   but now with improved error handling and modular design.")
    
    # Demonstrate programmatic equivalent
    print("\n🔄 Programmatic equivalent:")
    try:
        predictor = FibonacciBouncePredictor()
        result = predictor.run()  # This is equivalent to direct script execution
        
        if result.error:
            print(f"   ❌ Script execution failed: {result.error}")
        else:
            print(f"   ✅ Script execution successful")
            print(f"   📊 Result: {result.prediction} (prob: {result.probability:.4f})")
    
    except Exception as e:
        print(f"   ❌ Error in script compatibility demo: {e}")


def main():
    """Run all examples"""
    print("🚀 FIBONACCI BOUNCE PREDICTOR - USAGE EXAMPLES")
    print("=" * 80)
    print("This script demonstrates various ways to use the refactored predictions_5.py module")
    print("=" * 80)
    
    # Run all examples
    example_1_basic_usage()
    example_2_custom_configuration()
    example_3_dataframe_input()
    example_4_batch_processing()
    example_5_error_handling()
    example_6_model_information()
    example_7_direct_script_compatibility()
    
    print("\n" + "="*80)
    print("🎉 ALL EXAMPLES COMPLETED")
    print("="*80)
    print("\nFor more information, see:")
    print("- README_predictions_5_refactored.md")
    print("- predictions_5.py (source code with documentation)")


if __name__ == "__main__":
    main()
