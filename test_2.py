import candle_analyzer_2

# Parameters
symbol = "EURJPY"
timeframe = candle_analyzer_2.mt5.TIMEFRAME_M5
historical_candles = 1000
analysis_candles = 2

# Run the analysis
historical_filepath, analysis_filepaths = candle_analyzer_2.collect_and_analyze_data(
    symbol, timeframe, historical_candles, analysis_candles
)

# Use the saved analysis files in your decision-making logic
if analysis_filepaths:
    import json
    
    # Read Candle 1 analysis
    with open(analysis_filepaths['candle_1'], 'r') as f:
        candle_1_data = json.load(f)
        print("Candle 1 Price Action:", candle_1_data['candle']['price_action'])
    
    # Read Candle 2 analysis
    with open(analysis_filepaths['candle_2'], 'r') as f:
        candle_2_data = json.load(f)
        print("Candle 2 Price Action:", candle_2_data['candle']['price_action'])
    
    # Example decision-making logic
    if (candle_1_data['candle']['price_action'] == "BULLISH 🟢" and 
        candle_2_data['candle']['price_action'] == "BULLISH 🟢"):
        print("Decision: Consider buying due to consistent bullish trend.")
    else:
        print("Decision: Hold or evaluate further.")