import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timezone
import time
import os
import json

# Initialize connection to MT5
if not mt5.initialize():
    print("Failed to connect to MT5")
    mt5.shutdown()
    exit()

def get_mt5_server_time():
    """Get MT5 server time in UTC"""
    account_info = mt5.account_info()
    if account_info is not None:
        return datetime.now(timezone.utc)
    return datetime.now(timezone.utc)

def filter_trading_data(df, timezone_offset=0):
    """
    Filter data to include only:
    - Weekdays (Monday to Friday, weekday 0-4)
    - Trading hours (1 AM to 11 PM, hours 1-23)
    
    Args:
        df: DataFrame with 'time' column
        timezone_offset: Hours to adjust for broker timezone (default: 0 for UTC)
    
    Returns:
        Filtered DataFrame
    """
    if df is None or df.empty:
        return df
    
    # Ensure time column is datetime
    if 'time' in df.columns:
        df['time'] = pd.to_datetime(df['time'])
        
        # Adjust for broker timezone if needed
        if timezone_offset != 0:
            df['adjusted_time'] = df['time'] + pd.Timedelta(hours=timezone_offset)
        else:
            df['adjusted_time'] = df['time']
        
        # Filter for weekdays (Monday=0 to Friday=4)
        weekday_filter = df['adjusted_time'].dt.weekday < 5
        
        # Filter for trading hours (1 AM to 11 PM = hours 1-23)
        hour_filter = (df['adjusted_time'].dt.hour >= 1) & (df['adjusted_time'].dt.hour <= 23)
        
        # Combine filters
        combined_filter = weekday_filter & hour_filter
        
        # Apply filter
        filtered_df = df[combined_filter].copy()
        
        # Remove the temporary adjusted_time column
        if 'adjusted_time' in filtered_df.columns:
            filtered_df = filtered_df.drop('adjusted_time', axis=1)
        
        print(f"📊 DATA FILTERING RESULTS:")
        print(f"   Original candles: {len(df)}")
        print(f"   Filtered candles: {len(filtered_df)}")
        print(f"   Removed candles: {len(df) - len(filtered_df)}")
        print(f"   Filter criteria: Weekdays only (Mon-Fri), Hours 1-23 (1 AM - 11 PM)")
        
        return filtered_df
    else:
        print("❌ No 'time' column found in DataFrame")
        return df

def determine_price_action(candle_data):
    """Determine if candle is bullish, bearish, or doji"""
    open_price = candle_data['open']
    close_price = candle_data['close']
    if close_price > open_price:
        return "BULLISH 🟢"
    elif close_price < open_price:
        return "BEARISH 🔴"
    else:
        return "DOJI ➖"

def format_candle_times(candle_time, timeframe):
    """Calculate candle start and end times"""
    start_time = pd.to_datetime(candle_time, unit='s')
    duration_minutes = {
        mt5.TIMEFRAME_M1: 1,
        mt5.TIMEFRAME_M5: 5,
        mt5.TIMEFRAME_M15: 15,
        mt5.TIMEFRAME_M30: 30,
        mt5.TIMEFRAME_H1: 60
    }.get(timeframe, 5)
    end_time = start_time + pd.Timedelta(minutes=duration_minutes)
    return start_time, end_time

def get_historical_data(symbol, timeframe, num_candles=1000, apply_filter=True, timezone_offset=0):
    """
    Fetch historical candles with optional filtering
    
    Args:
        symbol: Trading symbol (e.g., 'EURJPY')
        timeframe: MT5 timeframe constant
        num_candles: Number of candles to fetch
        apply_filter: Whether to apply weekday/hour filtering
        timezone_offset: Hours to adjust for broker timezone
    """
    print(f"🔄 Fetching {num_candles} candles for {symbol}...")
    
    now = datetime.now(timezone.utc)
    rates = mt5.copy_rates_from(symbol, timeframe, now, num_candles)
    
    if rates is None or len(rates) == 0:
        print("❌ No historical data retrieved")
        return None
    
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    
    print(f"📈 Raw data retrieved: {len(df)} candles")
    print(f"📅 Date range: {df['time'].min()} to {df['time'].max()}")
    
    if apply_filter:
        print(f"\n🔍 APPLYING DATA FILTERS...")
        print("-" * 50)
        df = filter_trading_data(df, timezone_offset)
        
        if df.empty:
            print("❌ No data remaining after filtering!")
            return None
        
        print(f"📅 Filtered date range: {df['time'].min()} to {df['time'].max()}")
    else:
        print("ℹ️  No filtering applied - using all available data")
    
    return df

def save_data_to_file(df, symbol, timeframe, filtered=False):
    """Save historical data to a CSV file in the current directory"""
    timeframe_str = {
        mt5.TIMEFRAME_M1: "M1",
        mt5.TIMEFRAME_M5: "M5",
        mt5.TIMEFRAME_M15: "M15",
        mt5.TIMEFRAME_M30: "M30",
        mt5.TIMEFRAME_H1: "H1"
    }.get(timeframe, "M5")
    
    filter_suffix = "_filtered" if filtered else ""
    filename = f"{symbol}_{timeframe_str}_data{filter_suffix}.csv"
    filepath = os.path.join(os.getcwd(), filename)
    
    df.to_csv(filepath, index=False)
    print(f"💾 Historical data saved to: {filepath}")
    print(f"📊 Total candles saved: {len(df)}")
    
    return filepath

def get_latest_completed_candles(symbol, timeframe, num_candles=2, delay_seconds=2, apply_filter=True, timezone_offset=0):
    """
    Fetch the latest completed candles with optional filtering
    """
    time.sleep(delay_seconds)
    now = datetime.now(timezone.utc)
    
    # Calculate appropriate start time
    minute_divisors = {
        mt5.TIMEFRAME_M1: 1,
        mt5.TIMEFRAME_M5: 5,
        mt5.TIMEFRAME_M15: 15,
        mt5.TIMEFRAME_M30: 30,
        mt5.TIMEFRAME_H1: 60
    }
    divisor = minute_divisors.get(timeframe, 5)
    minutes = now.minute // divisor * divisor
    start_time = datetime(now.year, now.month, now.day, now.hour, minutes, tzinfo=timezone.utc)
    
    # Fetch extra candles in case some get filtered out
    extra_candles = 10 if apply_filter else 0
    rates = mt5.copy_rates_from(symbol, timeframe, start_time, num_candles + extra_candles + 1)
    
    if rates is None or len(rates) == 0:
        print("❌ No data retrieved for analysis")
        return None
    
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    
    if apply_filter:
        print(f"🔍 Filtering latest candles...")
        df = filter_trading_data(df, timezone_offset)
        
        if df.empty:
            print("❌ No candles remaining after filtering!")
            return None
    
    # Get the latest completed candles (excluding the current incomplete one)
    completed_candles = df.tail(num_candles + 1).head(num_candles)
    
    return completed_candles.iloc[::-1].reset_index(drop=True)

def analyze_candles(candles_df, timeframe):
    """Analyze candles and return structured data"""
    current_time = get_mt5_server_time()
    analysis = []
    
    for idx, candle in candles_df.iterrows():
        start_time, end_time = format_candle_times(candle['time'], timeframe)
        price_action = determine_price_action(candle)
        body_size = abs(candle['close'] - candle['open'])
        candle_range = candle['high'] - candle['low']
        
        # Additional analysis metrics
        upper_wick = candle['high'] - max(candle['open'], candle['close'])
        lower_wick = min(candle['open'], candle['close']) - candle['low']
        body_to_range_ratio = body_size / candle_range if candle_range > 0 else 0
        
        # Determine day of week and hour for verification
        candle_time = pd.to_datetime(candle['time'])
        day_name = candle_time.strftime('%A')
        hour = candle_time.hour
        
        candle_analysis = {
            "candle_number": idx + 1,
            "label": "LATEST" if idx == 0 else "PREVIOUS",
            "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "day_of_week": day_name,
            "hour": hour,
            "price_action": price_action,
            "open": candle['open'],
            "high": candle['high'],
            "low": candle['low'],
            "close": candle['close'],
            "volume": candle['tick_volume'],
            "spread": candle['spread'],
            "body_size": body_size,
            "range": candle_range,
            "upper_wick": upper_wick,
            "lower_wick": lower_wick,
            "body_to_range_ratio": body_to_range_ratio
        }
        analysis.append(candle_analysis)
    
    # Enhanced summary
    summary = {
        "latest_candle": analysis[0]["price_action"] if analysis else None,
        "previous_candle": analysis[1]["price_action"] if len(analysis) > 1 else None,
        "trend": None,
        "data_quality": "FILTERED (Weekdays, 1-23 hours)" if len(analysis) > 0 else "NO DATA"
    }
    
    if len(analysis) > 1:
        if analysis[0]['close'] > analysis[1]['close']:
            summary["trend"] = "UPWARD TREND 📈"
        elif analysis[0]['close'] < analysis[1]['close']:
            summary["trend"] = "DOWNWARD TREND 📉"
        else:
            summary["trend"] = "SIDEWAYS 🔄"
    
    return {
        "analysis_time": current_time.strftime('%Y-%m-%d %H:%M:%S UTC'),
        "candles": analysis,
        "summary": summary
    }

def display_candle_analysis(analysis_data):
    """Display formatted candle analysis with filter information"""
    print("=" * 90)
    print(f"🕐 ANALYSIS TIME: {analysis_data['analysis_time']}")
    print(f"🔍 DATA QUALITY: {analysis_data['summary']['data_quality']}")
    print("=" * 90)
    
    if analysis_data['candles']:
        print(f"\n📈 LATEST {len(analysis_data['candles'])} COMPLETED CANDLES")
        print("-" * 90)
        
        for candle in analysis_data['candles']:
            print(f"\n🕯️  CANDLE #{candle['candle_number']} ({candle['label']})")
            print(f"   📅 Day: {candle['day_of_week']}, Hour: {candle['hour']:02d}:00")
            print(f"   ⏰ Time Period: {candle['start_time']} → {candle['end_time'].split(' ')[1]}")
            print(f"   📊 Price Action: {candle['price_action']}")
            print(f"   💰 OPEN:  {candle['open']:.5f}")
            print(f"   📈 HIGH:  {candle['high']:.5f}")
            print(f"   📉 LOW:   {candle['low']:.5f}")
            print(f"   💰 CLOSE: {candle['close']:.5f}")
            print(f"   📊 Volume: {candle['volume']}")
            print(f"   📏 Spread: {candle['spread']}")
            print(f"   📐 Body Size: {candle['body_size']:.5f}")
            print(f"   📏 Range: {candle['range']:.5f}")
            print(f"   ⬆️  Upper Wick: {candle['upper_wick']:.5f}")
            print(f"   ⬇️  Lower Wick: {candle['lower_wick']:.5f}")
            print(f"   📊 Body/Range Ratio: {candle['body_to_range_ratio']:.3f}")
            print("-" * 50)
        
        print(f"\n📊 SUMMARY:")
        print(f"   Latest Candle (Candle 1): {analysis_data['summary']['latest_candle']}")
        if analysis_data['summary']['previous_candle']:
            print(f"   Previous Candle (Candle 2): {analysis_data['summary']['previous_candle']}")
        if analysis_data['summary']['trend']:
            print(f"   Short-term Trend: {analysis_data['summary']['trend']}")
        else:
            print("   Not enough data for trend analysis")
        print(f"   Data Quality: {analysis_data['summary']['data_quality']}")
    
    print("=" * 90)

def save_candle_analysis(analysis_data, symbol, timeframe, filtered=False):
    """Save each candle's analysis to separate JSON files"""
    timeframe_str = {
        mt5.TIMEFRAME_M1: "M1",
        mt5.TIMEFRAME_M5: "M5",
        mt5.TIMEFRAME_M15: "M15",
        mt5.TIMEFRAME_M30: "M30",
        mt5.TIMEFRAME_H1: "H1"
    }.get(timeframe, "M5")
    
    filter_suffix = "_filtered" if filtered else ""
    filepaths = {}
    
    for candle in analysis_data['candles']:
        candle_num = candle['candle_number']
        filename = f"{symbol}_{timeframe_str}_candle_{candle_num}_analysis{filter_suffix}.json"
        filepath = os.path.join(os.getcwd(), filename)
        
        candle_data = {
            "analysis_time": analysis_data['analysis_time'],
            "data_filters": {
                "weekdays_only": True,
                "hours_1_to_23": True,
                "description": "Data filtered for weekdays (Mon-Fri) and trading hours (1 AM - 11 PM)"
            },
            "candle": candle,
            "summary": analysis_data['summary']
        }
        
        with open(filepath, 'w') as f:
            json.dump(candle_data, f, indent=4)
        
        print(f"💾 Candle {candle_num} analysis saved to: {filepath}")
        filepaths[f"candle_{candle_num}"] = filepath
    
    return filepaths

def collect_and_analyze_data(symbol, timeframe, historical_candles=1000, analysis_candles=2, 
                           apply_filter=True, timezone_offset=0):
    """
    Collect historical data and analyze latest candles with filtering options
    
    Args:
        symbol: Trading symbol
        timeframe: MT5 timeframe
        historical_candles: Number of historical candles to collect
        analysis_candles: Number of latest candles to analyze
        apply_filter: Whether to apply weekday/hour filtering
        timezone_offset: Broker timezone offset in hours
    """
    print("🔄 COLLECTING HISTORICAL DATA...")
    print("-" * 60)
    
    if apply_filter:
        print("🔍 FILTER SETTINGS:")
        print("   • Weekdays only: Monday to Friday")
        print("   • Trading hours: 1 AM to 11 PM (hours 1-23)")
        print(f"   • Timezone offset: {timezone_offset} hours")
        print("-" * 60)
    
    historical_data = get_historical_data(symbol, timeframe, historical_candles, 
                                        apply_filter, timezone_offset)
    
    if historical_data is not None:
        historical_filepath = save_data_to_file(historical_data, symbol, timeframe, apply_filter)
        print(f"📈 Final data range: {historical_data['time'].min()} to {historical_data['time'].max()}")
        print()
        
        print("🔍 PERFORMING CANDLE ANALYSIS...")
        print("-" * 60)
        latest_candles = get_latest_completed_candles(symbol, timeframe, analysis_candles, 
                                                    apply_filter=apply_filter, 
                                                    timezone_offset=timezone_offset)
        
        if latest_candles is not None:
            analysis_data = analyze_candles(latest_candles, timeframe)
            display_candle_analysis(analysis_data)
            analysis_filepaths = save_candle_analysis(analysis_data, symbol, timeframe, apply_filter)
            return historical_filepath, analysis_filepaths
        else:
            print("❌ Failed to get latest candles for analysis")
            return historical_filepath, None
    else:
        print("❌ Failed to collect historical data")
        return None, None

if __name__ == "__main__":
    # Configuration
    symbol = "EURJPY"
    timeframe = mt5.TIMEFRAME_M5
    historical_candles = 1000
    analysis_candles = 2
    
    # Filter settings
    apply_data_filter = True  # Set to False to disable filtering
    broker_timezone_offset = 0  # Adjust if your broker uses a different timezone
    
    print("🚀 MT5 ENHANCED DATA ANALYZER")
    print("=" * 60)
    print(f"📊 Symbol: {symbol}")
    print(f"⏰ Timeframe: {timeframe}")
    print(f"📈 Historical candles: {historical_candles}")
    print(f"🔍 Analysis candles: {analysis_candles}")
    print(f"🎯 Data filtering: {'ENABLED' if apply_data_filter else 'DISABLED'}")
    print("=" * 60)
    
    historical_filepath, analysis_filepaths = collect_and_analyze_data(
        symbol=symbol, 
        timeframe=timeframe, 
        historical_candles=historical_candles, 
        analysis_candles=analysis_candles,
        apply_filter=apply_data_filter,
        timezone_offset=broker_timezone_offset
    )
    
    if historical_filepath and analysis_filepaths:
        print(f"\n✅ PROCESS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"📁 Historical data: {historical_filepath}")
        for candle_key, filepath in analysis_filepaths.items():
            candle_num = candle_key.split('_')[1]
            print(f"📁 Candle {candle_num} analysis: {filepath}")
        print("=" * 60)
    else:
        print(f"\n❌ PROCESS FAILED!")
        print("Please check your MT5 connection and symbol availability.")
    
    mt5.shutdown()