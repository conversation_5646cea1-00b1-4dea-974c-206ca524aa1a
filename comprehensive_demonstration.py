"""
Comprehensive Demonstration Script for Refactored predictions_5.py

This script systematically demonstrates all functionality of the refactored module,
validates backward compatibility, and showcases new programmatic capabilities.
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
import json
import time
import warnings
from datetime import datetime
from typing import Dict, List, Any

warnings.filterwarnings('ignore')

class ComprehensiveDemonstration:
    """Comprehensive demonstration suite for predictions_5.py refactoring"""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.data_file = None
        self.original_results = None
        self.test_data = None
        
    def setup_environment(self):
        """Setup demonstration environment and validate requirements"""
        print("🔧 SETTING UP DEMONSTRATION ENVIRONMENT")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # Check required files
        required_files = {
            "final_dl_model.keras": "TensorFlow model",
            "feature_engineering_pipeline.pkl": "Feature pipeline", 
            "selected_features.json": "Feature list",
            "predictions_5.py": "Refactored module"
        }
        
        missing_files = []
        for file_path, description in required_files.items():
            if os.path.exists(file_path):
                print(f"✅ {file_path} - {description}")
            else:
                print(f"❌ {file_path} - {description} (MISSING)")
                missing_files.append(file_path)
        
        # Find data file
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        if csv_files:
            self.data_file = csv_files[0]
            print(f"✅ Data file: {self.data_file}")
        else:
            print("❌ No CSV data files found")
            missing_files.append("*.csv")
        
        if missing_files:
            print(f"\n❌ Cannot proceed - missing files: {missing_files}")
            return False
        
        print(f"\n✅ Environment setup complete")
        return True
    
    def scenario_1_backward_compatibility(self):
        """Scenario 1: Test backward compatibility with direct script execution"""
        print("\n" + "=" * 60)
        print("SCENARIO 1: BACKWARD COMPATIBILITY VALIDATION")
        print("=" * 60)
        print("Testing that direct script execution works identically to original")
        
        try:
            print("\n🚀 Executing: python predictions_5.py")
            print("-" * 40)
            
            # Execute the script directly
            start_time = time.time()
            result = subprocess.run(
                [sys.executable, "predictions_5.py"],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            execution_time = time.time() - start_time
            
            print(f"⏱️ Execution time: {execution_time:.2f} seconds")
            print(f"🔢 Exit code: {result.returncode}")
            
            if result.returncode == 0:
                print("✅ Script executed successfully")
                
                # Display output
                if result.stdout:
                    print("\n📄 Script Output:")
                    print("-" * 40)
                    print(result.stdout)
                
                # Check for results file
                if os.path.exists("prediction_results.json"):
                    with open("prediction_results.json", 'r') as f:
                        results_data = json.load(f)
                    
                    if results_data:
                        latest_result = results_data[-1]
                        self.original_results = latest_result
                        
                        print("\n📊 Latest Prediction Result:")
                        print(f"   Symbol: {latest_result.get('symbol', 'N/A')}")
                        print(f"   Prediction: {latest_result.get('prediction', 'N/A')}")
                        print(f"   Probability: {latest_result.get('probability', 'N/A'):.4f}")
                        print(f"   Latest Price: {latest_result.get('latest_price', 'N/A'):.5f}")
                        
                        self.results['scenario_1'] = {
                            'success': True,
                            'execution_time': execution_time,
                            'prediction': latest_result.get('prediction'),
                            'probability': latest_result.get('probability'),
                            'latest_price': latest_result.get('latest_price')
                        }
                    else:
                        print("⚠️ Results file is empty")
                        self.results['scenario_1'] = {'success': False, 'error': 'Empty results file'}
                else:
                    print("⚠️ No results file created")
                    self.results['scenario_1'] = {'success': False, 'error': 'No results file'}
            else:
                print("❌ Script execution failed")
                if result.stderr:
                    print(f"Error output: {result.stderr}")
                self.results['scenario_1'] = {'success': False, 'error': result.stderr}
            
            print(f"\n📊 Scenario 1 Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ Script execution timed out")
            self.results['scenario_1'] = {'success': False, 'error': 'Timeout'}
            return False
        except Exception as e:
            print(f"❌ Error executing script: {e}")
            self.results['scenario_1'] = {'success': False, 'error': str(e)}
            return False
    
    def scenario_2_basic_api_usage(self):
        """Scenario 2: Test basic programmatic API usage"""
        print("\n" + "=" * 60)
        print("SCENARIO 2: BASIC PROGRAMMATIC API USAGE")
        print("=" * 60)
        print("Testing programmatic usage and comparing with direct script results")
        
        try:
            print("\n📦 Importing refactored module...")
            from predictions_5 import (
                FibonacciBouncePredictor,
                PredictorConfig,
                load_predictor,
                predict_from_file
            )
            print("✅ Import successful")
            
            # Test 1: Basic usage with default configuration
            print("\n🧪 Test 1: Default configuration")
            print("-" * 30)
            
            start_time = time.time()
            predictor = load_predictor()
            result = predictor.make_prediction()
            execution_time = time.time() - start_time
            
            print(f"⏱️ Execution time: {execution_time:.2f} seconds")
            
            if result.error:
                print(f"❌ Prediction failed: {result.error}")
                self.results['scenario_2'] = {'success': False, 'error': result.error}
                return False
            
            print("✅ Prediction successful")
            print(f"📊 Result Details:")
            print(f"   Symbol: {result.symbol}")
            print(f"   Prediction: {result.prediction}")
            print(f"   Probability: {result.probability:.4f}")
            print(f"   Latest Price: {result.latest_price:.5f}")
            print(f"   Candle Filtered: {result.incomplete_candle_filtered}")
            
            # Compare with original results
            if self.original_results:
                print(f"\n🔍 Comparison with Direct Script:")
                
                prediction_match = result.prediction == self.original_results.get('prediction')
                probability_diff = abs(result.probability - self.original_results.get('probability', 0))
                price_diff = abs(result.latest_price - self.original_results.get('latest_price', 0))
                
                print(f"   Prediction match: {'✅' if prediction_match else '❌'}")
                print(f"   Probability diff: {probability_diff:.10f}")
                print(f"   Price diff: {price_diff:.10f}")
                
                # Validate numerical consistency
                numerical_match = (prediction_match and 
                                 probability_diff < 1e-10 and 
                                 price_diff < 1e-10)
                
                if numerical_match:
                    print("✅ Perfect numerical match with direct script!")
                else:
                    print("⚠️ Numerical differences detected")
            
            # Test 2: Convenience function
            print("\n🧪 Test 2: Convenience function")
            print("-" * 30)
            
            result2 = predict_from_file(self.data_file)
            
            if result2.error:
                print(f"❌ Convenience function failed: {result2.error}")
            else:
                print("✅ Convenience function successful")
                
                # Compare results
                same_prediction = result.prediction == result2.prediction
                same_probability = abs(result.probability - result2.probability) < 1e-10
                
                print(f"   Consistency check: {'✅' if same_prediction and same_probability else '❌'}")
            
            self.results['scenario_2'] = {
                'success': True,
                'execution_time': execution_time,
                'prediction': result.prediction,
                'probability': result.probability,
                'numerical_match': numerical_match if self.original_results else True,
                'convenience_function_works': not result2.error
            }
            
            print(f"\n📊 Scenario 2 Result: ✅ PASSED")
            return True
            
        except Exception as e:
            print(f"❌ API usage failed: {e}")
            self.results['scenario_2'] = {'success': False, 'error': str(e)}
            return False
    
    def scenario_3_custom_configuration(self):
        """Scenario 3: Test custom configuration capabilities"""
        print("\n" + "=" * 60)
        print("SCENARIO 3: CUSTOM CONFIGURATION TESTING")
        print("=" * 60)
        print("Testing flexible configuration options")
        
        try:
            from predictions_5 import PredictorConfig, FibonacciBouncePredictor
            
            # Test 1: Verbose vs Silent mode
            print("\n🧪 Test 1: Verbose vs Silent configuration")
            print("-" * 40)
            
            # Verbose configuration
            config_verbose = PredictorConfig(
                data_file=self.data_file,
                verbose=True,
                symbol="TEST_VERBOSE"
            )
            
            # Silent configuration  
            config_silent = PredictorConfig(
                data_file=self.data_file,
                verbose=False,
                symbol="TEST_SILENT"
            )
            
            print("📊 Testing verbose mode...")
            predictor_verbose = FibonacciBouncePredictor(config_verbose)
            predictor_verbose.load_model_and_pipeline()
            predictor_verbose.load_data()
            result_verbose = predictor_verbose.make_prediction()
            
            print("\n📊 Testing silent mode...")
            predictor_silent = FibonacciBouncePredictor(config_silent)
            predictor_silent.load_model_and_pipeline()
            predictor_silent.load_data()
            result_silent = predictor_silent.make_prediction()
            
            # Compare results (should be identical despite different verbosity)
            if not (result_verbose.error or result_silent.error):
                prediction_match = result_verbose.prediction == result_silent.prediction
                probability_diff = abs(result_verbose.probability - result_silent.probability)
                
                print(f"\n🔍 Configuration Impact Analysis:")
                print(f"   Prediction consistency: {'✅' if prediction_match else '❌'}")
                print(f"   Probability difference: {probability_diff:.10f}")
                print(f"   Verbose symbol: {result_verbose.symbol}")
                print(f"   Silent symbol: {result_silent.symbol}")
                
                config_success = prediction_match and probability_diff < 1e-10
            else:
                print("❌ One or both configurations failed")
                config_success = False
            
            # Test 2: Custom file paths
            print(f"\n🧪 Test 2: Custom file path configuration")
            print("-" * 40)
            
            config_custom = PredictorConfig(
                model_path="final_dl_model.keras",  # Explicit path
                pipeline_path="feature_engineering_pipeline.pkl",  # Explicit path
                features_path="selected_features.json",  # Explicit path
                data_file=self.data_file,
                verbose=False
            )
            
            predictor_custom = FibonacciBouncePredictor(config_custom)
            
            # Test file checking
            file_status = predictor_custom._check_files()
            files_found = all(file_status.values())
            
            print(f"   File detection: {'✅' if files_found else '❌'}")
            print(f"   Model file: {'✅' if file_status.get('model', False) else '❌'}")
            print(f"   Pipeline file: {'✅' if file_status.get('pipeline', False) else '❌'}")
            print(f"   Features file: {'✅' if file_status.get('features', False) else '❌'}")
            print(f"   Data file: {'✅' if file_status.get('data', False) else '❌'}")
            
            self.results['scenario_3'] = {
                'success': config_success and files_found,
                'verbose_silent_match': config_success,
                'custom_paths_work': files_found,
                'probability_difference': probability_diff if not (result_verbose.error or result_silent.error) else None
            }
            
            print(f"\n📊 Scenario 3 Result: {'✅ PASSED' if config_success and files_found else '❌ FAILED'}")
            return config_success and files_found
            
        except Exception as e:
            print(f"❌ Configuration testing failed: {e}")
            self.results['scenario_3'] = {'success': False, 'error': str(e)}
            return False
    
    def scenario_4_dataframe_input(self):
        """Scenario 4: Test DataFrame input validation"""
        print("\n" + "=" * 60)
        print("SCENARIO 4: DATAFRAME INPUT VALIDATION")
        print("=" * 60)
        print("Testing flexible data input methods")
        
        try:
            from predictions_5 import predict_from_data, PredictorConfig
            
            # Load CSV data into DataFrame
            print("\n📊 Loading CSV data into DataFrame...")
            df = pd.read_csv(self.data_file)
            print(f"✅ DataFrame loaded: {df.shape}")
            print(f"   Columns: {list(df.columns)}")
            
            # Test DataFrame prediction
            print(f"\n🧪 Testing DataFrame prediction...")
            start_time = time.time()
            result_df = predict_from_data(
                data=df,
                config=PredictorConfig(verbose=False),
                symbol="DF_TEST"
            )
            execution_time = time.time() - start_time
            
            print(f"⏱️ DataFrame prediction time: {execution_time:.2f} seconds")
            
            if result_df.error:
                print(f"❌ DataFrame prediction failed: {result_df.error}")
                self.results['scenario_4'] = {'success': False, 'error': result_df.error}
                return False
            
            print("✅ DataFrame prediction successful")
            print(f"📊 Result Details:")
            print(f"   Symbol: {result_df.symbol}")
            print(f"   Prediction: {result_df.prediction}")
            print(f"   Probability: {result_df.probability:.4f}")
            
            # Compare with file-based prediction
            if self.original_results:
                print(f"\n🔍 Comparison with file-based prediction:")
                
                prediction_match = result_df.prediction == self.original_results.get('prediction')
                probability_diff = abs(result_df.probability - self.original_results.get('probability', 0))
                
                print(f"   Prediction match: {'✅' if prediction_match else '❌'}")
                print(f"   Probability difference: {probability_diff:.10f}")
                
                dataframe_match = prediction_match and probability_diff < 1e-10
            else:
                dataframe_match = True  # No comparison available
            
            # Test with data subset
            print(f"\n🧪 Testing with data subset...")
            df_subset = df.tail(500).copy()  # Last 500 rows
            
            result_subset = predict_from_data(
                data=df_subset,
                config=PredictorConfig(verbose=False),
                symbol="SUBSET_TEST"
            )
            
            if result_subset.error:
                print(f"⚠️ Subset prediction failed: {result_subset.error}")
                subset_success = False
            else:
                print("✅ Subset prediction successful")
                subset_success = True
                print(f"   Subset prediction: {result_subset.prediction}")
                print(f"   Subset probability: {result_subset.probability:.4f}")
            
            self.results['scenario_4'] = {
                'success': dataframe_match and subset_success,
                'execution_time': execution_time,
                'dataframe_match': dataframe_match,
                'subset_success': subset_success,
                'prediction': result_df.prediction,
                'probability': result_df.probability
            }
            
            print(f"\n📊 Scenario 4 Result: {'✅ PASSED' if dataframe_match and subset_success else '❌ FAILED'}")
            return dataframe_match and subset_success

        except Exception as e:
            print(f"❌ DataFrame input testing failed: {e}")
            self.results['scenario_4'] = {'success': False, 'error': str(e)}
            return False

    def scenario_5_batch_processing(self):
        """Scenario 5: Test batch processing capabilities"""
        print("\n" + "=" * 60)
        print("SCENARIO 5: BATCH PROCESSING DEMONSTRATION")
        print("=" * 60)
        print("Testing enhanced batch processing capabilities")

        try:
            from predictions_5 import batch_predict, PredictorConfig

            # Create multiple datasets from the main data
            print("\n📊 Creating multiple test datasets...")
            df = pd.read_csv(self.data_file)

            # Create different subsets to simulate different datasets
            datasets = [
                df.tail(300).copy(),  # Last 300 rows
                df.iloc[-500:-200].copy(),  # Middle section
                df.tail(400).copy()   # Last 400 rows
            ]

            symbols = ["BATCH_TEST_1", "BATCH_TEST_2", "BATCH_TEST_3"]

            print(f"✅ Created {len(datasets)} test datasets")
            for i, dataset in enumerate(datasets):
                print(f"   Dataset {i+1}: {dataset.shape} - Symbol: {symbols[i]}")

            # Test batch processing
            print(f"\n🧪 Testing batch prediction...")
            start_time = time.time()

            batch_results = batch_predict(
                data_list=datasets,
                config=PredictorConfig(verbose=False),
                symbols=symbols
            )

            execution_time = time.time() - start_time
            print(f"⏱️ Batch processing time: {execution_time:.2f} seconds")

            # Analyze batch results
            successful_predictions = 0
            failed_predictions = 0

            print(f"\n📊 Batch Results Analysis:")
            print("-" * 30)

            for i, result in enumerate(batch_results):
                if result.error:
                    print(f"   {symbols[i]}: ❌ FAILED - {result.error}")
                    failed_predictions += 1
                else:
                    print(f"   {symbols[i]}: ✅ SUCCESS")
                    print(f"     Prediction: {result.prediction}")
                    print(f"     Probability: {result.probability:.4f}")
                    successful_predictions += 1

            batch_success = failed_predictions == 0

            # Test individual vs batch consistency
            print(f"\n🔍 Individual vs Batch Consistency Check:")
            print("-" * 40)

            if successful_predictions > 0:
                # Test first dataset individually
                from predictions_5 import predict_from_data

                individual_result = predict_from_data(
                    data=datasets[0],
                    config=PredictorConfig(verbose=False),
                    symbol=symbols[0]
                )

                if not individual_result.error and not batch_results[0].error:
                    prediction_match = individual_result.prediction == batch_results[0].prediction
                    probability_diff = abs(individual_result.probability - batch_results[0].probability)

                    print(f"   Prediction match: {'✅' if prediction_match else '❌'}")
                    print(f"   Probability difference: {probability_diff:.10f}")

                    consistency_check = prediction_match and probability_diff < 1e-10
                else:
                    consistency_check = False
                    print("   ❌ Cannot compare due to errors")
            else:
                consistency_check = False

            self.results['scenario_5'] = {
                'success': batch_success and consistency_check,
                'execution_time': execution_time,
                'successful_predictions': successful_predictions,
                'failed_predictions': failed_predictions,
                'consistency_check': consistency_check
            }

            print(f"\n📊 Scenario 5 Result: {'✅ PASSED' if batch_success and consistency_check else '❌ FAILED'}")
            return batch_success and consistency_check

        except Exception as e:
            print(f"❌ Batch processing failed: {e}")
            self.results['scenario_5'] = {'success': False, 'error': str(e)}
            return False

    def scenario_6_error_handling(self):
        """Scenario 6: Test error handling validation"""
        print("\n" + "=" * 60)
        print("SCENARIO 6: ERROR HANDLING VALIDATION")
        print("=" * 60)
        print("Testing robust error handling capabilities")

        try:
            from predictions_5 import (
                FibonacciBouncePredictor,
                PredictorConfig,
                predict_from_data,
                ModelLoadError,
                DataError,
                FeatureError
            )

            error_tests_passed = 0
            total_error_tests = 4

            # Test 1: Missing model file
            print("\n🧪 Test 1: Missing model file")
            print("-" * 30)

            try:
                config = PredictorConfig(model_path="nonexistent_model.keras", verbose=False)
                predictor = FibonacciBouncePredictor(config)
                predictor.load_model_and_pipeline()
                print("❌ Should have raised ModelLoadError")
            except ModelLoadError as e:
                print(f"✅ ModelLoadError correctly raised: {e}")
                error_tests_passed += 1
            except Exception as e:
                print(f"⚠️ Unexpected exception type: {type(e).__name__}: {e}")

            # Test 2: Invalid data format
            print("\n🧪 Test 2: Invalid data format")
            print("-" * 30)

            try:
                invalid_df = pd.DataFrame({'invalid_column': [1, 2, 3]})
                result = predict_from_data(invalid_df, config=PredictorConfig(verbose=False))
                if result.error:
                    print(f"✅ Error properly handled in result: {result.error}")
                    error_tests_passed += 1
                else:
                    print("❌ Should have returned error in result")
            except DataError as e:
                print(f"✅ DataError correctly raised: {e}")
                error_tests_passed += 1
            except Exception as e:
                print(f"⚠️ Unexpected exception type: {type(e).__name__}: {e}")

            # Test 3: Non-existent file
            print("\n🧪 Test 3: Non-existent file")
            print("-" * 30)

            try:
                config = PredictorConfig(data_file="nonexistent_file.csv", verbose=False)
                predictor = FibonacciBouncePredictor(config)
                predictor.load_data()
                print("❌ Should have raised DataError")
            except DataError as e:
                print(f"✅ DataError correctly raised: {e}")
                error_tests_passed += 1
            except Exception as e:
                print(f"⚠️ Unexpected exception type: {type(e).__name__}: {e}")

            # Test 4: Graceful error handling in prediction result
            print("\n🧪 Test 4: Graceful error handling")
            print("-" * 30)

            try:
                # Create predictor without loading model
                config = PredictorConfig(verbose=False)
                predictor = FibonacciBouncePredictor(config)
                # Try to make prediction without loading model
                result = predictor.make_prediction()

                if result.error:
                    print(f"✅ Error gracefully handled: {result.error}")
                    error_tests_passed += 1
                else:
                    print("❌ Should have returned error for unloaded model")
            except Exception as e:
                print(f"⚠️ Exception not gracefully handled: {type(e).__name__}: {e}")

            error_handling_success = error_tests_passed >= 3  # Allow some flexibility

            print(f"\n📊 Error Handling Summary:")
            print(f"   Tests passed: {error_tests_passed}/{total_error_tests}")
            print(f"   Success rate: {(error_tests_passed/total_error_tests)*100:.1f}%")

            self.results['scenario_6'] = {
                'success': error_handling_success,
                'tests_passed': error_tests_passed,
                'total_tests': total_error_tests
            }

            print(f"\n📊 Scenario 6 Result: {'✅ PASSED' if error_handling_success else '❌ FAILED'}")
            return error_handling_success

        except Exception as e:
            print(f"❌ Error handling testing failed: {e}")
            self.results['scenario_6'] = {'success': False, 'error': str(e)}
            return False

    def scenario_7_performance_consistency(self):
        """Scenario 7: Test performance and consistency"""
        print("\n" + "=" * 60)
        print("SCENARIO 7: PERFORMANCE AND CONSISTENCY TESTING")
        print("=" * 60)
        print("Testing performance and numerical consistency")

        try:
            from predictions_5 import predict_from_file, PredictorConfig

            # Test multiple runs for consistency
            print("\n🧪 Testing prediction consistency across multiple runs...")

            num_runs = 3
            results = []
            execution_times = []

            for i in range(num_runs):
                print(f"   Run {i+1}/{num_runs}...")

                start_time = time.time()
                result = predict_from_file(
                    self.data_file,
                    config=PredictorConfig(verbose=False)
                )
                execution_time = time.time() - start_time

                if result.error:
                    print(f"   ❌ Run {i+1} failed: {result.error}")
                    self.results['scenario_7'] = {'success': False, 'error': f'Run {i+1} failed'}
                    return False

                results.append(result)
                execution_times.append(execution_time)
                print(f"   ✅ Run {i+1}: {result.prediction}, {result.probability:.6f}, {execution_time:.2f}s")

            # Analyze consistency
            print(f"\n📊 Consistency Analysis:")
            print("-" * 30)

            # Check prediction consistency
            predictions = [r.prediction for r in results]
            probabilities = [r.probability for r in results]

            prediction_consistent = len(set(predictions)) == 1
            probability_variance = np.var(probabilities)
            max_probability_diff = max(probabilities) - min(probabilities)

            print(f"   Prediction consistency: {'✅' if prediction_consistent else '❌'}")
            print(f"   Probability variance: {probability_variance:.2e}")
            print(f"   Max probability difference: {max_probability_diff:.2e}")

            # Performance analysis
            avg_execution_time = np.mean(execution_times)
            execution_variance = np.var(execution_times)

            print(f"\n📊 Performance Analysis:")
            print("-" * 30)
            print(f"   Average execution time: {avg_execution_time:.2f}s")
            print(f"   Execution time variance: {execution_variance:.4f}")
            print(f"   Min/Max execution time: {min(execution_times):.2f}s / {max(execution_times):.2f}s")

            # Success criteria
            consistency_success = (prediction_consistent and
                                 probability_variance < 1e-20 and
                                 max_probability_diff < 1e-10)

            performance_success = avg_execution_time < 120  # Should complete within 2 minutes

            overall_success = consistency_success and performance_success

            self.results['scenario_7'] = {
                'success': overall_success,
                'prediction_consistent': prediction_consistent,
                'probability_variance': float(probability_variance),
                'max_probability_diff': float(max_probability_diff),
                'avg_execution_time': avg_execution_time,
                'consistency_success': consistency_success,
                'performance_success': performance_success
            }

            print(f"\n📊 Scenario 7 Result: {'✅ PASSED' if overall_success else '❌ FAILED'}")
            return overall_success

        except Exception as e:
            print(f"❌ Performance testing failed: {e}")
            self.results['scenario_7'] = {'success': False, 'error': str(e)}
            return False

    def generate_comprehensive_report(self):
        """Generate comprehensive demonstration report"""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE DEMONSTRATION REPORT")
        print("=" * 80)

        total_time = time.time() - self.start_time

        # Count results
        total_scenarios = len(self.results)
        passed_scenarios = sum(1 for result in self.results.values() if result.get('success', False))
        success_rate = (passed_scenarios / total_scenarios) * 100 if total_scenarios > 0 else 0

        print(f"🕐 Total demonstration time: {total_time:.2f} seconds")
        print(f"📋 Total scenarios tested: {total_scenarios}")
        print(f"✅ Scenarios passed: {passed_scenarios}")
        print(f"❌ Scenarios failed: {total_scenarios - passed_scenarios}")
        print(f"🎯 Success rate: {success_rate:.1f}%")

        print(f"\n📋 Detailed Scenario Results:")
        print("-" * 50)

        scenario_names = {
            'scenario_1': 'Backward Compatibility',
            'scenario_2': 'Basic API Usage',
            'scenario_3': 'Custom Configuration',
            'scenario_4': 'DataFrame Input',
            'scenario_5': 'Batch Processing',
            'scenario_6': 'Error Handling',
            'scenario_7': 'Performance & Consistency'
        }

        for scenario_key, result in self.results.items():
            scenario_name = scenario_names.get(scenario_key, scenario_key)
            status = "✅ PASSED" if result.get('success', False) else "❌ FAILED"

            print(f"   {scenario_name}: {status}")

            # Add specific metrics for each scenario
            if scenario_key == 'scenario_1' and result.get('success'):
                print(f"     Execution time: {result.get('execution_time', 'N/A'):.2f}s")
                print(f"     Prediction: {result.get('prediction', 'N/A')}")
                print(f"     Probability: {result.get('probability', 'N/A'):.4f}")

            elif scenario_key == 'scenario_2' and result.get('success'):
                print(f"     Numerical match: {'✅' if result.get('numerical_match', False) else '❌'}")
                print(f"     Convenience function: {'✅' if result.get('convenience_function_works', False) else '❌'}")

            elif scenario_key == 'scenario_7' and result.get('success'):
                print(f"     Avg execution time: {result.get('avg_execution_time', 'N/A'):.2f}s")
                print(f"     Probability variance: {result.get('probability_variance', 'N/A'):.2e}")

            if not result.get('success', False) and 'error' in result:
                print(f"     Error: {result['error']}")

        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        print("-" * 30)

        if success_rate == 100:
            print("🎉 EXCELLENT: All scenarios passed successfully!")
            print("✅ The refactored module demonstrates:")
            print("   • Perfect backward compatibility")
            print("   • Robust programmatic API")
            print("   • Flexible configuration options")
            print("   • Comprehensive error handling")
            print("   • Consistent performance")
            print("   • Numerical precision maintained")

        elif success_rate >= 85:
            print("👍 GOOD: Most scenarios passed with minor issues")
            print("⚠️ Some areas may need attention")

        elif success_rate >= 70:
            print("⚠️ ACCEPTABLE: Core functionality works but improvements needed")

        else:
            print("❌ NEEDS WORK: Significant issues detected")

        print(f"\n📝 RECOMMENDATION:")
        if success_rate >= 85:
            print("✅ The refactored module is ready for production use!")
            print("   Users can confidently migrate from the original script.")
        else:
            print("⚠️ Address failing scenarios before production deployment.")

        return success_rate >= 85

    def run_comprehensive_demonstration(self):
        """Run the complete comprehensive demonstration"""
        print("🚀 COMPREHENSIVE DEMONSTRATION OF REFACTORED PREDICTIONS_5.PY")
        print("=" * 80)
        print("This demonstration validates all functionality and proves equivalence")
        print("=" * 80)

        # Setup
        if not self.setup_environment():
            print("❌ Environment setup failed - cannot proceed")
            return False

        # Run all scenarios
        scenarios = [
            ("Backward Compatibility", self.scenario_1_backward_compatibility),
            ("Basic API Usage", self.scenario_2_basic_api_usage),
            ("Custom Configuration", self.scenario_3_custom_configuration),
            ("DataFrame Input", self.scenario_4_dataframe_input),
            ("Batch Processing", self.scenario_5_batch_processing),
            ("Error Handling", self.scenario_6_error_handling),
            ("Performance & Consistency", self.scenario_7_performance_consistency)
        ]

        for scenario_name, scenario_func in scenarios:
            try:
                print(f"\n🎬 Starting {scenario_name}...")
                scenario_func()
            except Exception as e:
                print(f"🔥 CRITICAL ERROR in {scenario_name}: {e}")
                self.results[f'scenario_{len(self.results)+1}'] = {
                    'success': False,
                    'error': f'Critical error: {str(e)}'
                }

        # Generate final report
        success = self.generate_comprehensive_report()

        print(f"\n🏁 DEMONSTRATION COMPLETE")
        print("=" * 80)

        return success


def main():
    """Main execution function"""
    demo = ComprehensiveDemonstration()
    success = demo.run_comprehensive_demonstration()

    # Save results to file
    with open("demonstration_results.json", "w") as f:
        json.dump(demo.results, f, indent=2, default=str)

    print(f"\n💾 Results saved to: demonstration_results.json")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
