"""
Rigorous validation script to compare original vs refactored predictions_5.py

This script performs comprehensive validation to ensure the refactored module
produces identical prediction results to the original script.
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import pickle
import tempfile
import shutil
from datetime import datetime
import subprocess
import time

# Suppress warnings for clean output
import warnings
warnings.filterwarnings('ignore')

class ValidationSuite:
    """Comprehensive validation suite for predictions_5.py refactoring"""
    
    def __init__(self):
        self.results = {}
        self.test_data = None
        self.temp_dir = None
        self.original_cwd = os.getcwd()
        
    def setup(self):
        """Setup validation environment"""
        print("🔧 Setting up validation environment...")
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp()
        print(f"📁 Created temp directory: {self.temp_dir}")
        
        # Check required files exist
        required_files = [
            "final_dl_model.keras",
            "feature_engineering_pipeline.pkl",
            "selected_features.json"
        ]
        
        missing_files = [f for f in required_files if not os.path.exists(f)]
        if missing_files:
            print(f"❌ Missing required files: {missing_files}")
            return False
        
        # Find data file
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        if not csv_files:
            print("❌ No CSV data files found")
            return False
        
        self.data_file = csv_files[0]
        print(f"✅ Using data file: {self.data_file}")
        
        return True
    
    def teardown(self):
        """Cleanup validation environment"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"🗑️ Cleaned up temp directory")
    
    def test_basic_import(self):
        """Test 1: Basic import functionality"""
        print("\n" + "="*60)
        print("TEST 1: Basic Import Functionality")
        print("="*60)
        
        try:
            # Test import without TensorFlow loading
            print("📦 Testing basic imports...")
            
            # Import configuration classes first (lightweight)
            from predictions_5 import PredictorConfig, PredictionResult
            print("✅ Configuration classes imported successfully")
            
            # Test configuration creation
            config = PredictorConfig(verbose=False)
            print(f"✅ Configuration created: symbol={config.symbol}")
            
            # Test result structure
            result = PredictionResult(
                prediction_time="2024-01-01 12:00:00",
                symbol="TEST",
                prediction=1,
                probability=0.75,
                prediction_based_on_candle="2024-01-01 11:55:00",
                prediction_candle_close=150.25,
                latest_price=150.30,
                latest_candle_time="2024-01-01 12:00:00",
                incomplete_candle_filtered=True
            )
            print(f"✅ PredictionResult created: {result.symbol}")
            
            self.results['basic_import'] = True
            return True
            
        except Exception as e:
            print(f"❌ Basic import failed: {e}")
            self.results['basic_import'] = False
            return False
    
    def test_feature_engineering_consistency(self):
        """Test 2: Feature engineering consistency"""
        print("\n" + "="*60)
        print("TEST 2: Feature Engineering Consistency")
        print("="*60)
        
        try:
            # Load sample data
            print("📊 Loading sample data...")
            df = pd.read_csv(self.data_file)
            print(f"✅ Loaded {len(df)} rows of data")
            
            # Test feature engineering pipeline loading
            print("🔧 Testing feature engineering pipeline...")
            
            # Import feature engineering components
            from predictions_5 import FeatureEngineeringPipeline
            
            # Load the pipeline
            pipeline = FeatureEngineeringPipeline.load(
                "feature_engineering_pipeline.pkl", 
                verbose=False
            )
            print("✅ Pipeline loaded successfully")
            
            # Prepare data (minimal processing to test feature creation)
            df['time'] = pd.to_datetime(df['time'])
            df = df.set_index('time')
            
            # Keep only OHLC columns for testing
            test_data = df[['open', 'high', 'low', 'close']].tail(100).copy()
            
            # Test base feature creation
            print("🔧 Testing base feature creation...")
            features = pipeline.create_base_features(test_data)
            print(f"✅ Created {len(features.columns)} features from {len(features)} rows")
            
            # Validate feature creation statistics
            stats = pipeline.get_feature_stats()
            print(f"📊 Feature stats: {stats}")
            
            self.results['feature_engineering'] = {
                'success': True,
                'input_rows': len(test_data),
                'output_features': len(features.columns),
                'stats': stats
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Feature engineering test failed: {e}")
            self.results['feature_engineering'] = {'success': False, 'error': str(e)}
            return False
    
    def test_configuration_system(self):
        """Test 3: Configuration system"""
        print("\n" + "="*60)
        print("TEST 3: Configuration System")
        print("="*60)
        
        try:
            from predictions_5 import PredictorConfig
            
            # Test default configuration
            config1 = PredictorConfig()
            print(f"✅ Default config: {config1.symbol}, verbose={config1.verbose}")
            
            # Test custom configuration
            config2 = PredictorConfig(
                model_path="test_model.keras",
                symbol="TESTPAIR",
                verbose=False,
                auto_find_data=False
            )
            print(f"✅ Custom config: {config2.symbol}, verbose={config2.verbose}")
            
            # Validate configuration attributes
            assert config2.model_path == "test_model.keras"
            assert config2.symbol == "TESTPAIR"
            assert config2.verbose == False
            assert config2.auto_find_data == False
            
            print("✅ Configuration validation passed")
            
            self.results['configuration'] = True
            return True
            
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            self.results['configuration'] = False
            return False
    
    def test_error_handling(self):
        """Test 4: Error handling"""
        print("\n" + "="*60)
        print("TEST 4: Error Handling")
        print("="*60)
        
        try:
            from predictions_5 import (
                FibonacciBouncePredictor, 
                PredictorConfig,
                ModelLoadError,
                DataError,
                FeatureError
            )
            
            # Test 1: Invalid model path
            print("🧪 Testing invalid model path...")
            try:
                config = PredictorConfig(model_path="nonexistent.keras", verbose=False)
                predictor = FibonacciBouncePredictor(config)
                predictor.load_model_and_pipeline()
                print("❌ Should have raised ModelLoadError")
                return False
            except ModelLoadError:
                print("✅ ModelLoadError correctly raised")
            
            # Test 2: Invalid data
            print("🧪 Testing invalid data...")
            try:
                config = PredictorConfig(verbose=False)
                predictor = FibonacciBouncePredictor(config)
                invalid_df = pd.DataFrame({'invalid': [1, 2, 3]})
                predictor.load_data_from_dataframe(invalid_df)
                print("❌ Should have raised DataError")
                return False
            except DataError:
                print("✅ DataError correctly raised")
            
            # Test 3: Non-existent file
            print("🧪 Testing non-existent file...")
            try:
                config = PredictorConfig(verbose=False)
                predictor = FibonacciBouncePredictor(config)
                predictor.load_data("nonexistent.csv")
                print("❌ Should have raised DataError")
                return False
            except DataError:
                print("✅ DataError correctly raised for missing file")
            
            print("✅ All error handling tests passed")
            self.results['error_handling'] = True
            return True
            
        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
            self.results['error_handling'] = False
            return False
    
    def test_data_loading_consistency(self):
        """Test 5: Data loading consistency"""
        print("\n" + "="*60)
        print("TEST 5: Data Loading Consistency")
        print("="*60)
        
        try:
            from predictions_5 import FibonacciBouncePredictor, PredictorConfig
            
            # Load data via CSV file
            print("📊 Testing CSV file loading...")
            config = PredictorConfig(data_file=self.data_file, verbose=False)
            predictor1 = FibonacciBouncePredictor(config)
            success1 = predictor1.load_data()
            
            if not success1:
                print("❌ CSV loading failed")
                return False
            
            print(f"✅ CSV loaded: {predictor1.data.shape}")
            
            # Load same data via DataFrame
            print("📊 Testing DataFrame loading...")
            df = pd.read_csv(self.data_file)
            predictor2 = FibonacciBouncePredictor(PredictorConfig(verbose=False))
            success2 = predictor2.load_data_from_dataframe(df, "TESTPAIR")
            
            if not success2:
                print("❌ DataFrame loading failed")
                return False
            
            print(f"✅ DataFrame loaded: {predictor2.data.shape}")
            
            # Compare data shapes and content
            if predictor1.data.shape != predictor2.data.shape:
                print(f"❌ Shape mismatch: {predictor1.data.shape} vs {predictor2.data.shape}")
                return False
            
            # Compare data content (allowing for minor floating point differences)
            data_diff = np.abs(predictor1.data.values - predictor2.data.values).max()
            if data_diff > 1e-10:
                print(f"❌ Data content differs by {data_diff}")
                return False
            
            print("✅ Data loading consistency validated")
            
            self.results['data_loading'] = {
                'success': True,
                'csv_shape': predictor1.data.shape,
                'dataframe_shape': predictor2.data.shape,
                'max_difference': float(data_diff)
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Data loading test failed: {e}")
            self.results['data_loading'] = {'success': False, 'error': str(e)}
            return False
    
    def test_api_functions(self):
        """Test 6: Public API functions"""
        print("\n" + "="*60)
        print("TEST 6: Public API Functions")
        print("="*60)
        
        try:
            from predictions_5 import (
                check_dependencies,
                get_model_info,
                PredictorConfig
            )
            
            # Test dependency checking
            print("🔍 Testing dependency checking...")
            deps_verbose = check_dependencies(verbose=True)
            deps_silent = check_dependencies(verbose=False)
            
            if deps_verbose != deps_silent:
                print("❌ Dependency check inconsistent")
                return False
            
            print(f"✅ Dependencies available: {deps_verbose}")
            
            # Test model info
            print("📊 Testing model info...")
            info = get_model_info(PredictorConfig(verbose=False))
            
            if not isinstance(info, dict):
                print("❌ Model info should return dict")
                return False
            
            print(f"✅ Model info keys: {list(info.keys())}")
            
            self.results['api_functions'] = {
                'success': True,
                'dependencies_available': deps_verbose,
                'model_info_keys': list(info.keys())
            }
            
            return True
            
        except Exception as e:
            print(f"❌ API functions test failed: {e}")
            self.results['api_functions'] = {'success': False, 'error': str(e)}
            return False
    
    def run_validation_suite(self):
        """Run complete validation suite"""
        print("🚀 RIGOROUS VALIDATION SUITE FOR PREDICTIONS_5.PY REFACTORING")
        print("="*80)
        
        if not self.setup():
            print("❌ Setup failed, cannot continue validation")
            return False
        
        # Run all tests
        tests = [
            ("Basic Import", self.test_basic_import),
            ("Feature Engineering", self.test_feature_engineering_consistency),
            ("Configuration System", self.test_configuration_system),
            ("Error Handling", self.test_error_handling),
            ("Data Loading", self.test_data_loading_consistency),
            ("API Functions", self.test_api_functions)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} test...")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"🔥 {test_name}: ERROR - {e}")
        
        # Print summary
        self.print_validation_summary(passed, total)
        
        # Cleanup
        self.teardown()
        
        return passed == total
    
    def print_validation_summary(self, passed, total):
        """Print validation summary"""
        print("\n" + "="*80)
        print("📊 VALIDATION SUMMARY")
        print("="*80)
        
        success_rate = (passed / total) * 100
        
        print(f"Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {total - passed}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        for test_name, result in self.results.items():
            if isinstance(result, dict):
                status = "✅ PASSED" if result.get('success', False) else "❌ FAILED"
                print(f"   {test_name}: {status}")
                if not result.get('success', False) and 'error' in result:
                    print(f"     Error: {result['error']}")
            else:
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"   {test_name}: {status}")
        
        if success_rate >= 80:
            print(f"\n🎉 VALIDATION SUITE: PASSED")
            print("The refactored module demonstrates strong compatibility and functionality.")
        else:
            print(f"\n⚠️ VALIDATION SUITE: NEEDS ATTENTION")
            print("Some tests failed and require investigation.")
        
        print("\n📝 Next Steps:")
        if success_rate == 100:
            print("✅ All tests passed - proceed with numerical comparison testing")
        else:
            print("🔧 Address failed tests before proceeding to numerical validation")


if __name__ == "__main__":
    validator = ValidationSuite()
    success = validator.run_validation_suite()
    
    if success:
        print("\n🚀 Ready for numerical comparison testing!")
    else:
        print("\n⚠️ Fix validation issues before proceeding")
    
    sys.exit(0 if success else 1)
