"""
Numerical validation script for predictions_5.py refactoring.

This script performs direct numerical comparison between original and refactored
implementations to ensure identical prediction results.
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import subprocess
import tempfile
import shutil
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

class NumericalValidator:
    """Performs numerical validation between original and refactored implementations"""
    
    def __init__(self):
        self.temp_dir = None
        self.data_file = None
        self.validation_results = {}
        
    def setup(self):
        """Setup validation environment"""
        print("🔧 Setting up numerical validation environment...")
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp()
        print(f"📁 Created temp directory: {self.temp_dir}")
        
        # Check required files
        required_files = [
            "final_dl_model.keras",
            "feature_engineering_pipeline.pkl",
            "selected_features.json"
        ]
        
        missing_files = [f for f in required_files if not os.path.exists(f)]
        if missing_files:
            print(f"❌ Missing required files: {missing_files}")
            return False
        
        # Find data file
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        if not csv_files:
            print("❌ No CSV data files found")
            return False
        
        self.data_file = csv_files[0]
        print(f"✅ Using data file: {self.data_file}")
        
        return True
    
    def teardown(self):
        """Cleanup validation environment"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"🗑️ Cleaned up temp directory")
    
    def test_data_preprocessing_consistency(self):
        """Test that data preprocessing produces identical results"""
        print("\n" + "="*60)
        print("NUMERICAL TEST 1: Data Preprocessing Consistency")
        print("="*60)
        
        try:
            # Load raw data
            print("📊 Loading raw data...")
            raw_data = pd.read_csv(self.data_file)
            print(f"✅ Loaded {len(raw_data)} rows")
            
            # Test refactored data processing
            print("🔧 Testing refactored data processing...")
            from predictions_5 import FibonacciBouncePredictor, PredictorConfig
            
            config = PredictorConfig(verbose=False)
            predictor = FibonacciBouncePredictor(config)
            
            # Process data using refactored method
            processed_data = predictor._validate_and_process_data(raw_data.copy())
            print(f"✅ Refactored processing: {processed_data.shape}")
            
            # Validate data structure
            expected_columns = ['open', 'high', 'low', 'close']
            if 'volume' in raw_data.columns or 'tick_volume' in raw_data.columns:
                expected_columns.append('volume')
            
            missing_cols = [col for col in expected_columns if col not in processed_data.columns]
            if missing_cols:
                print(f"❌ Missing columns: {missing_cols}")
                return False
            
            # Check data types
            numeric_cols = ['open', 'high', 'low', 'close']
            for col in numeric_cols:
                if not pd.api.types.is_numeric_dtype(processed_data[col]):
                    print(f"❌ Column {col} is not numeric")
                    return False
            
            # Check for NaN values
            nan_count = processed_data.isna().sum().sum()
            print(f"📊 NaN values after processing: {nan_count}")
            
            # Validate OHLC relationships
            invalid_ohlc = (
                (processed_data['high'] < processed_data['low']) |
                (processed_data['high'] < processed_data['open']) |
                (processed_data['high'] < processed_data['close']) |
                (processed_data['low'] > processed_data['open']) |
                (processed_data['low'] > processed_data['close'])
            ).sum()
            
            if invalid_ohlc > 0:
                print(f"❌ Invalid OHLC relationships: {invalid_ohlc}")
                return False
            
            print("✅ Data preprocessing validation passed")
            
            self.validation_results['data_preprocessing'] = {
                'success': True,
                'input_rows': len(raw_data),
                'output_rows': len(processed_data),
                'output_columns': list(processed_data.columns),
                'nan_count': int(nan_count),
                'invalid_ohlc': int(invalid_ohlc)
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Data preprocessing test failed: {e}")
            self.validation_results['data_preprocessing'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_feature_engineering_numerical_consistency(self):
        """Test that feature engineering produces numerically consistent results"""
        print("\n" + "="*60)
        print("NUMERICAL TEST 2: Feature Engineering Consistency")
        print("="*60)
        
        try:
            # Load and prepare test data
            print("📊 Preparing test data...")
            raw_data = pd.read_csv(self.data_file)
            
            from predictions_5 import FibonacciBouncePredictor, PredictorConfig
            
            config = PredictorConfig(verbose=False)
            predictor = FibonacciBouncePredictor(config)
            
            # Process data
            processed_data = predictor._validate_and_process_data(raw_data.copy())
            
            # Use a subset for testing (last 200 rows for speed)
            test_data = processed_data.tail(200).copy()
            print(f"✅ Using {len(test_data)} rows for feature testing")
            
            # Load feature engineering pipeline
            print("🔧 Loading feature engineering pipeline...")
            from predictions_5 import FeatureEngineeringPipeline
            
            pipeline = FeatureEngineeringPipeline.load(
                "feature_engineering_pipeline.pkl",
                verbose=False
            )
            
            # Create base features
            print("🔧 Creating base features...")
            features = pipeline.create_base_features(test_data)
            print(f"✅ Created {len(features.columns)} features")
            
            # Validate feature creation
            feature_stats = pipeline.get_feature_stats()
            print(f"📊 Feature creation stats: {feature_stats}")
            
            # Check for critical features
            critical_features = [
                'swing_high_20', 'swing_low_20', 'swing_range_20',
                'fib_382_20', 'fib_618_20',
                'sma_10', 'sma_20', 'sma_50',
                'momentum_3', 'momentum_5',
                'atr_14', 'volatility_ratio'
            ]
            
            missing_critical = [f for f in critical_features if f not in features.columns]
            if missing_critical:
                print(f"⚠️ Missing critical features: {missing_critical}")
            else:
                print("✅ All critical features present")
            
            # Validate numerical properties
            numeric_features = features.select_dtypes(include=[np.number])
            
            # Check for infinite values
            inf_count = np.isinf(numeric_features.values).sum()
            print(f"📊 Infinite values: {inf_count}")
            
            # Check for NaN values
            nan_count = numeric_features.isna().sum().sum()
            print(f"📊 NaN values: {nan_count}")
            
            # Validate feature ranges (basic sanity checks)
            feature_ranges = {}
            for col in numeric_features.columns:
                if not numeric_features[col].isna().all():
                    feature_ranges[col] = {
                        'min': float(numeric_features[col].min()),
                        'max': float(numeric_features[col].max()),
                        'mean': float(numeric_features[col].mean()),
                        'std': float(numeric_features[col].std())
                    }
            
            print(f"✅ Feature engineering numerical validation passed")
            
            self.validation_results['feature_engineering'] = {
                'success': True,
                'input_rows': len(test_data),
                'output_features': len(features.columns),
                'critical_features_present': len(missing_critical) == 0,
                'infinite_values': int(inf_count),
                'nan_values': int(nan_count),
                'feature_count': len(numeric_features.columns),
                'sample_feature_ranges': dict(list(feature_ranges.items())[:5])  # First 5 for brevity
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Feature engineering test failed: {e}")
            self.validation_results['feature_engineering'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_candle_filtering_consistency(self):
        """Test that candle filtering produces consistent results"""
        print("\n" + "="*60)
        print("NUMERICAL TEST 3: Candle Filtering Consistency")
        print("="*60)
        
        try:
            # Load test data
            print("📊 Loading test data...")
            raw_data = pd.read_csv(self.data_file)
            
            from predictions_5 import FibonacciBouncePredictor, PredictorConfig
            
            config = PredictorConfig(verbose=False)
            predictor = FibonacciBouncePredictor(config)
            
            # Process and load data
            processed_data = predictor._validate_and_process_data(raw_data.copy())
            predictor.data = processed_data
            
            # Test candle filtering
            print("🔧 Testing candle filtering...")
            original_length = len(processed_data)
            
            # Get data and filter
            data = predictor.get_data()
            filtered_data, was_filtered = predictor.filter_incomplete_candle(data)
            
            print(f"📊 Original length: {original_length}")
            print(f"📊 Filtered length: {len(filtered_data)}")
            print(f"📊 Was filtered: {was_filtered}")
            
            # Validate filtering logic
            if was_filtered:
                expected_length = original_length - 1
                if len(filtered_data) != expected_length:
                    print(f"❌ Filtering length mismatch: expected {expected_length}, got {len(filtered_data)}")
                    return False
                
                # Check that the last candle was removed
                original_last_time = data.index[-1]
                filtered_last_time = filtered_data.index[-1]
                
                if filtered_last_time >= original_last_time:
                    print(f"❌ Last candle not properly filtered")
                    return False
                
                print("✅ Candle filtering logic validated")
            else:
                print("ℹ️ No filtering applied (insufficient data)")
            
            # Validate data integrity after filtering
            if len(filtered_data) > 0:
                # Check OHLC integrity
                invalid_ohlc = (
                    (filtered_data['high'] < filtered_data['low']) |
                    (filtered_data['high'] < filtered_data['open']) |
                    (filtered_data['high'] < filtered_data['close']) |
                    (filtered_data['low'] > filtered_data['open']) |
                    (filtered_data['low'] > filtered_data['close'])
                ).sum()
                
                if invalid_ohlc > 0:
                    print(f"❌ Invalid OHLC after filtering: {invalid_ohlc}")
                    return False
                
                print("✅ Data integrity maintained after filtering")
            
            self.validation_results['candle_filtering'] = {
                'success': True,
                'original_length': original_length,
                'filtered_length': len(filtered_data),
                'was_filtered': was_filtered,
                'data_integrity_maintained': True
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Candle filtering test failed: {e}")
            self.validation_results['candle_filtering'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_configuration_numerical_impact(self):
        """Test that different configurations produce expected numerical differences"""
        print("\n" + "="*60)
        print("NUMERICAL TEST 4: Configuration Impact")
        print("="*60)
        
        try:
            from predictions_5 import PredictorConfig, FibonacciBouncePredictor
            
            # Test verbose vs non-verbose (should not affect numerical results)
            print("🔧 Testing verbose configuration impact...")
            
            config_verbose = PredictorConfig(verbose=True, data_file=self.data_file)
            config_silent = PredictorConfig(verbose=False, data_file=self.data_file)
            
            predictor_verbose = FibonacciBouncePredictor(config_verbose)
            predictor_silent = FibonacciBouncePredictor(config_silent)
            
            # Load data with both configurations
            success_verbose = predictor_verbose.load_data()
            success_silent = predictor_silent.load_data()
            
            if not (success_verbose and success_silent):
                print("❌ Data loading failed for one configuration")
                return False
            
            # Compare data shapes
            if predictor_verbose.data.shape != predictor_silent.data.shape:
                print(f"❌ Data shape differs: {predictor_verbose.data.shape} vs {predictor_silent.data.shape}")
                return False
            
            # Compare data content
            data_diff = np.abs(predictor_verbose.data.values - predictor_silent.data.values).max()
            if data_diff > 1e-10:
                print(f"❌ Data content differs by {data_diff}")
                return False
            
            print("✅ Configuration impact validation passed")
            
            self.validation_results['configuration_impact'] = {
                'success': True,
                'verbose_shape': predictor_verbose.data.shape,
                'silent_shape': predictor_silent.data.shape,
                'max_difference': float(data_diff)
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Configuration impact test failed: {e}")
            self.validation_results['configuration_impact'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def run_numerical_validation(self):
        """Run complete numerical validation suite"""
        print("🔢 NUMERICAL VALIDATION SUITE FOR PREDICTIONS_5.PY")
        print("="*80)
        print("This suite validates numerical consistency of the refactored implementation")
        print("="*80)
        
        if not self.setup():
            print("❌ Setup failed, cannot continue validation")
            return False
        
        # Run numerical tests
        tests = [
            ("Data Preprocessing", self.test_data_preprocessing_consistency),
            ("Feature Engineering", self.test_feature_engineering_numerical_consistency),
            ("Candle Filtering", self.test_candle_filtering_consistency),
            ("Configuration Impact", self.test_configuration_numerical_impact)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} test...")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"🔥 {test_name}: ERROR - {e}")
        
        # Print summary
        self.print_numerical_summary(passed, total)
        
        # Cleanup
        self.teardown()
        
        return passed == total
    
    def print_numerical_summary(self, passed, total):
        """Print numerical validation summary"""
        print("\n" + "="*80)
        print("📊 NUMERICAL VALIDATION SUMMARY")
        print("="*80)
        
        success_rate = (passed / total) * 100
        
        print(f"Total Numerical Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {total - passed}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 Detailed Numerical Results:")
        for test_name, result in self.validation_results.items():
            if isinstance(result, dict):
                status = "✅ PASSED" if result.get('success', False) else "❌ FAILED"
                print(f"   {test_name}: {status}")
                
                # Print key metrics
                if result.get('success', False):
                    for key, value in result.items():
                        if key not in ['success', 'error'] and not key.startswith('sample_'):
                            print(f"     {key}: {value}")
                else:
                    if 'error' in result:
                        print(f"     Error: {result['error']}")
        
        if success_rate == 100:
            print(f"\n🎉 NUMERICAL VALIDATION: PASSED")
            print("All numerical consistency tests passed successfully!")
        elif success_rate >= 75:
            print(f"\n⚠️ NUMERICAL VALIDATION: MOSTLY PASSED")
            print("Most tests passed, but some issues need attention.")
        else:
            print(f"\n❌ NUMERICAL VALIDATION: FAILED")
            print("Significant numerical inconsistencies detected.")


if __name__ == "__main__":
    validator = NumericalValidator()
    success = validator.run_numerical_validation()
    
    if success:
        print("\n🚀 Numerical validation completed successfully!")
        print("Ready for full prediction comparison testing.")
    else:
        print("\n⚠️ Numerical validation issues detected.")
        print("Address these before proceeding to prediction testing.")
    
    sys.exit(0 if success else 1)
