"""
Fixed Comprehensive Demonstration Script for Refactored predictions_5.py

This script addresses the Unicode and pickle loading issues found in the initial demonstration.
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
import json
import time
import warnings
from datetime import datetime

warnings.filterwarnings('ignore')

class FixedDemonstration:
    """Fixed demonstration suite addressing Unicode and pickle issues"""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.data_file = None
        
    def setup_environment(self):
        """Setup demonstration environment"""
        print("SETTING UP DEMONSTRATION ENVIRONMENT")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # Check required files
        required_files = [
            "final_dl_model.keras",
            "feature_engineering_pipeline.pkl", 
            "selected_features.json",
            "predictions_5.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"OK: {file_path}")
            else:
                print(f"MISSING: {file_path}")
                missing_files.append(file_path)
        
        # Find data file
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        if csv_files:
            self.data_file = csv_files[0]
            print(f"Data file: {self.data_file}")
        else:
            print("No CSV data files found")
            missing_files.append("*.csv")
        
        if missing_files:
            print(f"Cannot proceed - missing files: {missing_files}")
            return False
        
        print("Environment setup complete")
        return True
    
    def test_direct_script_execution(self):
        """Test direct script execution with fixed Unicode handling"""
        print("\nTEST 1: DIRECT SCRIPT EXECUTION")
        print("=" * 50)
        
        try:
            print("Executing: python predictions_5.py")
            
            # Set environment to handle Unicode properly
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            start_time = time.time()
            result = subprocess.run(
                [sys.executable, "predictions_5.py"],
                capture_output=True,
                text=True,
                timeout=180,
                env=env,
                encoding='utf-8',
                errors='replace'
            )
            execution_time = time.time() - start_time
            
            print(f"Execution time: {execution_time:.2f} seconds")
            print(f"Exit code: {result.returncode}")
            
            if result.returncode == 0:
                print("SUCCESS: Script executed successfully")
                
                # Check for results file
                if os.path.exists("prediction_results.json"):
                    with open("prediction_results.json", 'r') as f:
                        results_data = json.load(f)
                    
                    if results_data:
                        latest_result = results_data[-1]
                        print(f"Prediction: {latest_result.get('prediction', 'N/A')}")
                        print(f"Probability: {latest_result.get('probability', 'N/A'):.4f}")
                        print(f"Symbol: {latest_result.get('symbol', 'N/A')}")
                        
                        self.results['direct_execution'] = {
                            'success': True,
                            'execution_time': execution_time,
                            'prediction': latest_result.get('prediction'),
                            'probability': latest_result.get('probability')
                        }
                        return True
                    else:
                        print("WARNING: Results file is empty")
                else:
                    print("WARNING: No results file created")
            else:
                print("FAILED: Script execution failed")
                if result.stderr:
                    print(f"Error: {result.stderr[:500]}...")  # Truncate long errors
                
            self.results['direct_execution'] = {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'error': result.stderr if result.returncode != 0 else None
            }
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"ERROR: {e}")
            self.results['direct_execution'] = {'success': False, 'error': str(e)}
            return False
    
    def test_basic_api_import(self):
        """Test basic API import functionality"""
        print("\nTEST 2: BASIC API IMPORT")
        print("=" * 50)
        
        try:
            print("Testing imports...")
            
            # Import in the same process to avoid pickle issues
            import predictions_5
            
            print("SUCCESS: Module imported")
            
            # Test configuration
            config = predictions_5.PredictorConfig(verbose=False)
            print(f"Config created: {config.symbol}")
            
            # Test result structure
            result = predictions_5.PredictionResult(
                prediction_time="2024-01-01 12:00:00",
                symbol="TEST",
                prediction=1,
                probability=0.75,
                prediction_based_on_candle="2024-01-01 11:55:00",
                prediction_candle_close=150.25,
                latest_price=150.30,
                latest_candle_time="2024-01-01 12:00:00",
                incomplete_candle_filtered=True
            )
            print(f"PredictionResult created: {result.symbol}")
            
            # Test dependency checking
            deps_ok = predictions_5.check_dependencies(verbose=False)
            print(f"Dependencies available: {deps_ok}")
            
            self.results['api_import'] = {
                'success': True,
                'dependencies_available': deps_ok
            }
            
            print("SUCCESS: API import test passed")
            return True
            
        except Exception as e:
            print(f"FAILED: API import failed: {e}")
            self.results['api_import'] = {'success': False, 'error': str(e)}
            return False
    
    def test_data_processing(self):
        """Test data processing functionality"""
        print("\nTEST 3: DATA PROCESSING")
        print("=" * 50)
        
        try:
            import predictions_5
            
            # Load data
            print(f"Loading data from: {self.data_file}")
            df = pd.read_csv(self.data_file)
            print(f"Data loaded: {df.shape}")
            
            # Test data processing
            config = predictions_5.PredictorConfig(verbose=False)
            predictor = predictions_5.FibonacciBouncePredictor(config)
            
            # Test data validation
            processed_data = predictor._validate_and_process_data(df.copy())
            print(f"Data processed: {processed_data.shape}")
            
            # Test DataFrame loading
            success = predictor.load_data_from_dataframe(df.copy(), "TEST_SYMBOL")
            print(f"DataFrame loading: {'SUCCESS' if success else 'FAILED'}")
            
            if success:
                print(f"Loaded data shape: {predictor.data.shape}")
                print(f"Symbol: {predictor.symbol}")
            
            self.results['data_processing'] = {
                'success': success,
                'input_shape': df.shape,
                'processed_shape': processed_data.shape if success else None
            }
            
            print(f"RESULT: {'SUCCESS' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            print(f"FAILED: Data processing failed: {e}")
            self.results['data_processing'] = {'success': False, 'error': str(e)}
            return False
    
    def test_error_handling(self):
        """Test error handling capabilities"""
        print("\nTEST 4: ERROR HANDLING")
        print("=" * 50)
        
        try:
            import predictions_5
            
            tests_passed = 0
            total_tests = 3
            
            # Test 1: Invalid model path
            print("Testing invalid model path...")
            try:
                config = predictions_5.PredictorConfig(model_path="nonexistent.keras", verbose=False)
                predictor = predictions_5.FibonacciBouncePredictor(config)
                predictor.load_model_and_pipeline()
                print("FAILED: Should have raised exception")
            except predictions_5.ModelLoadError:
                print("SUCCESS: ModelLoadError raised correctly")
                tests_passed += 1
            except Exception as e:
                print(f"PARTIAL: Different exception raised: {type(e).__name__}")
                tests_passed += 0.5
            
            # Test 2: Invalid data
            print("Testing invalid data...")
            try:
                invalid_df = pd.DataFrame({'invalid': [1, 2, 3]})
                config = predictions_5.PredictorConfig(verbose=False)
                predictor = predictions_5.FibonacciBouncePredictor(config)
                predictor.load_data_from_dataframe(invalid_df)
                print("FAILED: Should have raised exception")
            except predictions_5.DataError:
                print("SUCCESS: DataError raised correctly")
                tests_passed += 1
            except Exception as e:
                print(f"PARTIAL: Different exception raised: {type(e).__name__}")
                tests_passed += 0.5
            
            # Test 3: Non-existent file
            print("Testing non-existent file...")
            try:
                config = predictions_5.PredictorConfig(verbose=False)
                predictor = predictions_5.FibonacciBouncePredictor(config)
                predictor.load_data("nonexistent.csv")
                print("FAILED: Should have raised exception")
            except predictions_5.DataError:
                print("SUCCESS: DataError raised correctly")
                tests_passed += 1
            except Exception as e:
                print(f"PARTIAL: Different exception raised: {type(e).__name__}")
                tests_passed += 0.5
            
            success_rate = tests_passed / total_tests
            error_handling_success = success_rate >= 0.7
            
            print(f"Error handling tests: {tests_passed}/{total_tests}")
            print(f"Success rate: {success_rate*100:.1f}%")
            
            self.results['error_handling'] = {
                'success': error_handling_success,
                'tests_passed': tests_passed,
                'total_tests': total_tests
            }
            
            print(f"RESULT: {'SUCCESS' if error_handling_success else 'FAILED'}")
            return error_handling_success
            
        except Exception as e:
            print(f"FAILED: Error handling test failed: {e}")
            self.results['error_handling'] = {'success': False, 'error': str(e)}
            return False
    
    def generate_report(self):
        """Generate final demonstration report"""
        print("\n" + "=" * 60)
        print("DEMONSTRATION REPORT")
        print("=" * 60)
        
        total_time = time.time() - self.start_time
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result.get('success', False))
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Tests run: {total_tests}")
        print(f"Tests passed: {passed_tests}")
        print(f"Success rate: {success_rate:.1f}%")
        
        print(f"\nDetailed Results:")
        test_names = {
            'direct_execution': 'Direct Script Execution',
            'api_import': 'API Import Test',
            'data_processing': 'Data Processing',
            'error_handling': 'Error Handling'
        }
        
        for test_key, result in self.results.items():
            test_name = test_names.get(test_key, test_key)
            status = "PASSED" if result.get('success', False) else "FAILED"
            print(f"  {test_name}: {status}")
            
            if not result.get('success', False) and 'error' in result:
                error_msg = result['error']
                if len(error_msg) > 100:
                    error_msg = error_msg[:100] + "..."
                print(f"    Error: {error_msg}")
        
        print(f"\nOVERALL ASSESSMENT:")
        if success_rate >= 75:
            print("SUCCESS: Core functionality validated")
            print("The refactored module demonstrates:")
            print("  - Basic import and configuration capabilities")
            print("  - Data processing functionality")
            print("  - Error handling mechanisms")
            if self.results.get('direct_execution', {}).get('success', False):
                print("  - Backward compatibility with direct execution")
        else:
            print("NEEDS ATTENTION: Some core functionality issues detected")
        
        return success_rate >= 75
    
    def run_demonstration(self):
        """Run the fixed demonstration"""
        print("FIXED COMPREHENSIVE DEMONSTRATION")
        print("=" * 60)
        print("Testing refactored predictions_5.py with issue fixes")
        print("=" * 60)
        
        if not self.setup_environment():
            return False
        
        # Run core tests
        tests = [
            self.test_direct_script_execution,
            self.test_basic_api_import,
            self.test_data_processing,
            self.test_error_handling
        ]
        
        for test_func in tests:
            try:
                test_func()
            except Exception as e:
                print(f"CRITICAL ERROR in test: {e}")
        
        # Generate report
        success = self.generate_report()
        
        # Save results
        with open("fixed_demonstration_results.json", "w") as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\nResults saved to: fixed_demonstration_results.json")
        return success


def main():
    """Main execution function"""
    demo = FixedDemonstration()
    success = demo.run_demonstration()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
