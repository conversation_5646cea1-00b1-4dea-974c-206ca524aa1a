{"direct_execution": {"success": false, "execution_time": 18.34876322746277, "error": "2025-07-30 22:30:54.784897: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-07-30 22:30:57.992234: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n2025-07-30 22:31:05,876 - __main__.FibonacciBouncePredictor - INFO - Starting model and pipeline loading\n2025-07-30 22:31:05.890814: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\nTo enable the following instructions: SSE3 SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild Tensor<PERSON>low with the appropriate compiler flags.\n2025-07-30 22:31:06,193 - __main__.FibonacciBouncePredictor - INFO - Model loaded from final_dl_model.keras\n2025-07-30 22:31:06,224 - __main__.FibonacciBouncePredictor - INFO - Pipeline loaded successfully from feature_engineering_pipeline.pkl\n2025-07-30 22:31:06,225 - __main__.FibonacciBouncePredictor - INFO - Features loaded: 52 features\n2025-07-30 22:31:06,225 - __main__.FibonacciBouncePredictor - INFO - Loading data from F:\\production_2\\EURJPY_M5_data_filtered.csv\n2025-07-30 22:31:06,232 - __main__.FibonacciBouncePredictor - INFO - Data loaded: 952 rows\n2025-07-30 22:31:06,241 - __main__.FibonacciBouncePredictor - INFO - Data processing completed: 952 candles\n2025-07-30 22:31:06,241 - __main__.FibonacciBouncePredictor - INFO - Starting prediction process\n2025-07-30 22:31:06,241 - __main__.FibonacciBouncePredictor - INFO - Filtering incomplete candle. Last candle: 2025-07-23 09:30:00\n2025-07-30 22:31:06,242 - __main__.FibonacciBouncePredictor - INFO - Using candle from: 2025-07-23 09:25:00\n2025-07-30 22:31:06,242 - __main__.FibonacciBouncePredictor - INFO - Processing features\n2025-07-30 22:31:06,242 - __main__.FibonacciBouncePredictor - INFO - Starting base feature creation\n2025-07-30 22:31:07,255 - __main__.FibonacciBouncePredictor - INFO - Feature creation completed: {'initial_features': 5, 'final_features': 95, 'created_features': 90, 'data_rows': 951}\n2025-07-30 22:31:07,353 - __main__.FibonacciBouncePredictor - ERROR - Error during feature transformation: 'SymbolicFeatureGenerator' object has no attribute 'verbose'\n2025-07-30 22:31:07,353 - __main__.FibonacciBouncePredictor - ERROR - Prediction failed: Error during feature transformation: 'SymbolicFeatureGenerator' object has no attribute 'verbose'\n"}, "api_import": {"success": true, "dependencies_available": true}, "data_processing": {"success": true, "input_shape": [952, 8], "processed_shape": [952, 5]}, "error_handling": {"success": true, "tests_passed": 3, "total_tests": 3}}