# Migration Guide: From Original to Refactored predictions_5.py

This guide helps you migrate from the original `predictions_5.py` script to the refactored modular library while maintaining identical prediction results.

## Overview of Changes

The refactored module provides the same core functionality with these improvements:

✅ **Preserved**: All prediction algorithms and feature engineering logic  
✅ **Preserved**: Identical prediction results  
✅ **Preserved**: File format requirements  
✅ **Enhanced**: Modular design for programmatic usage  
✅ **Enhanced**: Comprehensive error handling  
✅ **Enhanced**: Configurable parameters  
✅ **Enhanced**: Better logging and debugging  

## Migration Scenarios

### Scenario 1: Direct Script Execution (No Changes Required)

**Original Usage:**
```bash
python predictions_5.py
```

**Refactored Usage:**
```bash
python predictions_5.py  # Still works exactly the same!
```

**Result:** Identical behavior and output. No migration needed.

### Scenario 2: Importing for Programmatic Use

**Original Approach (Not Possible):**
The original script was not designed for import and programmatic use.

**Refactored Approach:**
```python
from predictions_5 import FibonacciBouncePredictor

# Basic usage
predictor = FibonacciBouncePredictor()
result = predictor.run()

# Advanced usage
predictor.load_model_and_pipeline()
predictor.load_data("my_data.csv")
result = predictor.make_prediction()
```

### Scenario 3: Custom Configuration

**Original Approach:**
Required modifying the script source code to change file paths or parameters.

**Refactored Approach:**
```python
from predictions_5 import PredictorConfig, load_predictor

config = PredictorConfig(
    model_path="custom_model.keras",
    data_file="custom_data.csv",
    symbol="GBPUSD",
    verbose=False
)

predictor = load_predictor(config)
result = predictor.make_prediction()
```

### Scenario 4: Batch Processing

**Original Approach:**
Required running the script multiple times or modifying source code.

**Refactored Approach:**
```python
from predictions_5 import batch_predict
import pandas as pd

# Load multiple datasets
data_list = [
    pd.read_csv("EURJPY_data.csv"),
    pd.read_csv("GBPUSD_data.csv"),
    pd.read_csv("USDJPY_data.csv")
]

# Batch process
results = batch_predict(data_list, symbols=["EURJPY", "GBPUSD", "USDJPY"])

for result in results:
    print(f"{result.symbol}: {result.prediction}")
```

## API Mapping

### Class and Function Mapping

| Original | Refactored | Notes |
|----------|------------|-------|
| `FibonacciBouncePredictor` class | `FibonacciBouncePredictor` class | Enhanced with better error handling |
| `FeatureEngineeringPipeline` class | `FeatureEngineeringPipeline` class | Enhanced with modular methods |
| `SymbolicFeatureGenerator` class | `SymbolicFeatureGenerator` class | Enhanced with better logging |
| Direct execution | `predictor.run()` method | Programmatic equivalent |
| N/A | `load_predictor()` function | New convenience function |
| N/A | `predict_from_data()` function | New convenience function |
| N/A | `predict_from_file()` function | New convenience function |
| N/A | `batch_predict()` function | New convenience function |

### Configuration Mapping

| Original | Refactored | Notes |
|----------|------------|-------|
| Hardcoded file paths | `PredictorConfig.model_path` | Configurable |
| Hardcoded file paths | `PredictorConfig.pipeline_path` | Configurable |
| Hardcoded file paths | `PredictorConfig.features_path` | Configurable |
| Auto-discovery only | `PredictorConfig.data_file` | Explicit file specification |
| Hardcoded symbol | `PredictorConfig.symbol` | Configurable |
| Always verbose | `PredictorConfig.verbose` | Configurable |
| No logging config | `PredictorConfig.logging_level` | Configurable |

### Error Handling Mapping

| Original | Refactored | Notes |
|----------|------------|-------|
| Generic exceptions | `ModelLoadError` | Specific model loading errors |
| Generic exceptions | `DataError` | Specific data processing errors |
| Generic exceptions | `FeatureError` | Specific feature engineering errors |
| Print-only errors | Structured error results | Programmatic error handling |

## Step-by-Step Migration

### Step 1: Verify Compatibility

1. **Test Direct Execution:**
   ```bash
   python predictions_5.py
   ```
   Should work exactly as before.

2. **Test Import:**
   ```python
   from predictions_5 import check_dependencies
   print(check_dependencies())
   ```

### Step 2: Migrate Simple Usage

**Before:**
```bash
python predictions_5.py > results.txt
```

**After:**
```python
from predictions_5 import FibonacciBouncePredictor

predictor = FibonacciBouncePredictor()
result = predictor.run()

# Save results programmatically
with open("results.txt", "w") as f:
    f.write(f"Prediction: {result.prediction}\n")
    f.write(f"Probability: {result.probability}\n")
```

### Step 3: Migrate Advanced Usage

**Before (modifying source code):**
```python
# Had to edit the script to change these
self.model_path = "custom_model.keras"
self.data_file = "custom_data.csv"
```

**After:**
```python
from predictions_5 import PredictorConfig, load_predictor

config = PredictorConfig(
    model_path="custom_model.keras",
    data_file="custom_data.csv"
)

predictor = load_predictor(config)
result = predictor.make_prediction()
```

### Step 4: Add Error Handling

**Before:**
```python
# Limited error handling
try:
    # Run script
    pass
except Exception as e:
    print(f"Error: {e}")
```

**After:**
```python
from predictions_5 import load_predictor, ModelLoadError, DataError

try:
    predictor = load_predictor()
    result = predictor.make_prediction()
    
    if result.error:
        print(f"Prediction failed: {result.error}")
    else:
        print(f"Success: {result.prediction}")
        
except ModelLoadError as e:
    print(f"Model loading failed: {e}")
except DataError as e:
    print(f"Data processing failed: {e}")
```

## Validation Checklist

Use this checklist to ensure successful migration:

- [ ] **Direct script execution works**: `python predictions_5.py` produces same results
- [ ] **Import works**: Can import classes and functions without errors
- [ ] **Predictions match**: Programmatic predictions match script execution results
- [ ] **File requirements**: Same model and data files work with both versions
- [ ] **Error handling**: Errors are caught and handled appropriately
- [ ] **Configuration**: Custom configurations work as expected
- [ ] **Performance**: No significant performance degradation

## Common Migration Issues

### Issue 1: Import Errors

**Problem:** `ImportError` when importing the module

**Solution:** 
- Ensure all dependencies are installed: `pip install pandas numpy tensorflow`
- Check Python version compatibility (3.7+)
- Verify the refactored file is in the Python path

### Issue 2: Type Annotation Errors

**Problem:** `TypeError` related to type annotations

**Solution:**
- Ensure Python 3.7+ is being used
- The refactored module uses compatible type annotations

### Issue 3: File Path Issues

**Problem:** Model or data files not found

**Solution:**
```python
# Use absolute paths or verify working directory
config = PredictorConfig(
    model_path=os.path.abspath("final_dl_model.keras"),
    data_file=os.path.abspath("data.csv")
)
```

### Issue 4: Different Results

**Problem:** Predictions differ between original and refactored versions

**Solution:**
- Verify same model files are being used
- Check data preprocessing is identical
- Ensure same random seeds (if applicable)
- Run validation tests to compare outputs

## Testing Migration

### Basic Validation Test

```python
# Test script to validate migration
from predictions_5 import FibonacciBouncePredictor
import subprocess
import json

# Run original script
original_result = subprocess.run(
    ["python", "predictions_5.py"], 
    capture_output=True, 
    text=True
)

# Run refactored version
predictor = FibonacciBouncePredictor()
refactored_result = predictor.run()

# Compare results (implement your comparison logic)
print("Migration validation:")
print(f"Original completed: {original_result.returncode == 0}")
print(f"Refactored completed: {not refactored_result.error}")
```

## Support and Troubleshooting

If you encounter issues during migration:

1. **Check the examples**: Review `examples_predictions_5.py` for usage patterns
2. **Read the documentation**: See `README_predictions_5_refactored.md` for detailed API docs
3. **Test incrementally**: Start with simple usage and gradually add complexity
4. **Validate results**: Use the test suite to ensure identical predictions
5. **Check logs**: Enable verbose mode and logging for debugging

## Benefits After Migration

Once migrated, you'll have access to:

- **Programmatic Control**: Call predictions from your own code
- **Batch Processing**: Process multiple datasets efficiently
- **Better Error Handling**: Specific exceptions and error messages
- **Flexible Configuration**: Customize all parameters without editing source
- **Improved Debugging**: Structured logging and verbose modes
- **Future Extensibility**: Modular design allows easy enhancements

The refactored module maintains 100% backward compatibility while providing these enhanced capabilities for modern development workflows.
