"""
Quick validation test for predictions_5.py refactoring.
Tests core functionality without heavy TensorFlow operations.
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import warnings

warnings.filterwarnings('ignore')

def test_basic_imports():
    """Test basic imports work correctly"""
    print("🧪 Testing basic imports...")
    
    try:
        # Test configuration imports
        from predictions_5 import PredictorConfig, PredictionResult
        print("✅ Configuration classes imported")
        
        # Test configuration creation
        config = PredictorConfig(verbose=False)
        print(f"✅ Config created: {config.symbol}")
        
        # Test result creation
        result = PredictionResult(
            prediction_time="2024-01-01 12:00:00",
            symbol="TEST",
            prediction=1,
            probability=0.75,
            prediction_based_on_candle="2024-01-01 11:55:00",
            prediction_candle_close=150.25,
            latest_price=150.30,
            latest_candle_time="2024-01-01 12:00:00",
            incomplete_candle_filtered=True
        )
        print(f"✅ PredictionResult created: {result.symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_data_processing():
    """Test data processing functionality"""
    print("\n🧪 Testing data processing...")
    
    try:
        # Find CSV file
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        if not csv_files:
            print("⚠️ No CSV files found, skipping data test")
            return True
        
        data_file = csv_files[0]
        print(f"📊 Using data file: {data_file}")
        
        # Load raw data
        raw_data = pd.read_csv(data_file)
        print(f"✅ Loaded {len(raw_data)} rows")
        
        # Test refactored data processing
        from predictions_5 import FibonacciBouncePredictor, PredictorConfig
        
        config = PredictorConfig(verbose=False)
        predictor = FibonacciBouncePredictor(config)
        
        # Test data validation and processing
        processed_data = predictor._validate_and_process_data(raw_data.copy())
        print(f"✅ Data processed: {processed_data.shape}")
        
        # Validate expected columns
        expected_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in expected_cols if col not in processed_data.columns]
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
            return False
        
        print("✅ All required columns present")
        
        # Test data loading methods
        success1 = predictor.load_data_from_dataframe(raw_data.copy(), "TESTPAIR")
        if not success1:
            print("❌ DataFrame loading failed")
            return False
        
        print("✅ DataFrame loading successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processing test failed: {e}")
        return False

def test_feature_engineering():
    """Test feature engineering without full pipeline loading"""
    print("\n🧪 Testing feature engineering...")
    
    try:
        # Check if pipeline file exists
        if not os.path.exists("feature_engineering_pipeline.pkl"):
            print("⚠️ Pipeline file not found, skipping feature test")
            return True
        
        # Create sample data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='5T')
        np.random.seed(42)
        
        data = []
        base_price = 150.0
        current_price = base_price
        
        for date in dates:
            change = np.random.normal(0, 0.001) * current_price
            current_price += change
            
            volatility = np.random.uniform(0.0005, 0.002) * current_price
            open_price = current_price + np.random.uniform(-volatility/2, volatility/2)
            high_price = max(open_price, current_price) + np.random.uniform(0, volatility)
            low_price = min(open_price, current_price) - np.random.uniform(0, volatility)
            close_price = current_price
            
            data.append({
                'time': date,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': np.random.randint(100, 1000)
            })
        
        df = pd.DataFrame(data)
        df['time'] = pd.to_datetime(df['time'])
        df = df.set_index('time')
        test_data = df[['open', 'high', 'low', 'close']].copy()
        
        print(f"✅ Created test data: {test_data.shape}")
        
        # Test feature engineering pipeline loading
        from predictions_5 import FeatureEngineeringPipeline
        
        pipeline = FeatureEngineeringPipeline.load(
            "feature_engineering_pipeline.pkl",
            verbose=False
        )
        print("✅ Pipeline loaded")
        
        # Test base feature creation
        features = pipeline.create_base_features(test_data)
        print(f"✅ Features created: {features.shape}")
        
        # Validate feature creation
        if len(features.columns) <= len(test_data.columns):
            print("❌ No new features created")
            return False
        
        print(f"✅ Created {len(features.columns) - len(test_data.columns)} new features")
        
        return True
        
    except Exception as e:
        print(f"❌ Feature engineering test failed: {e}")
        return False

def test_error_handling():
    """Test error handling functionality"""
    print("\n🧪 Testing error handling...")
    
    try:
        from predictions_5 import (
            FibonacciBouncePredictor,
            PredictorConfig,
            ModelLoadError,
            DataError
        )
        
        # Test invalid model path
        try:
            config = PredictorConfig(model_path="nonexistent.keras", verbose=False)
            predictor = FibonacciBouncePredictor(config)
            predictor.load_model_and_pipeline()
            print("❌ Should have raised ModelLoadError")
            return False
        except ModelLoadError:
            print("✅ ModelLoadError correctly raised")
        
        # Test invalid data
        try:
            config = PredictorConfig(verbose=False)
            predictor = FibonacciBouncePredictor(config)
            invalid_df = pd.DataFrame({'invalid': [1, 2, 3]})
            predictor.load_data_from_dataframe(invalid_df)
            print("❌ Should have raised DataError")
            return False
        except DataError:
            print("✅ DataError correctly raised")
        
        print("✅ Error handling validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_api_functions():
    """Test public API functions"""
    print("\n🧪 Testing API functions...")
    
    try:
        from predictions_5 import check_dependencies, get_model_info, PredictorConfig
        
        # Test dependency checking
        deps_available = check_dependencies(verbose=False)
        print(f"✅ Dependencies check: {deps_available}")
        
        # Test model info
        info = get_model_info(PredictorConfig(verbose=False))
        if not isinstance(info, dict):
            print("❌ Model info should return dict")
            return False
        
        print(f"✅ Model info retrieved: {len(info)} keys")
        
        return True
        
    except Exception as e:
        print(f"❌ API functions test failed: {e}")
        return False

def main():
    """Run quick validation tests"""
    print("⚡ QUICK VALIDATION SUITE FOR PREDICTIONS_5.PY REFACTORING")
    print("="*70)
    print("Testing core functionality without heavy TensorFlow operations")
    print("="*70)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Data Processing", test_data_processing),
        ("Feature Engineering", test_feature_engineering),
        ("Error Handling", test_error_handling),
        ("API Functions", test_api_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"🔥 {test_name}: ERROR - {e}")
    
    # Print summary
    print("\n" + "="*70)
    print("📊 QUICK VALIDATION SUMMARY")
    print("="*70)
    
    success_rate = (passed / total) * 100
    
    print(f"Total Tests: {total}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {total - passed}")
    print(f"🎯 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print(f"\n🎉 QUICK VALIDATION: PASSED")
        print("Core functionality is working correctly!")
        print("\n📝 Next Steps:")
        print("1. ✅ Basic functionality validated")
        print("2. 🔄 Ready for full prediction testing (requires model loading)")
        print("3. 📊 Can proceed with numerical comparison")
    elif success_rate >= 80:
        print(f"\n⚠️ QUICK VALIDATION: MOSTLY PASSED")
        print("Most core functionality works, minor issues detected.")
    else:
        print(f"\n❌ QUICK VALIDATION: FAILED")
        print("Significant issues with core functionality.")
    
    print("\n" + "="*70)
    
    return success_rate == 100

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
