import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timezone
import time
import os

# Initialize connection to MT5
if not mt5.initialize():
    print("Failed to connect to MT5")
    mt5.shutdown()
    exit()

def get_mt5_server_time():
    """Get MT5 server time"""
    account_info = mt5.account_info()
    if account_info is not None:
        server_time = datetime.now(timezone.utc)
        return server_time
    return datetime.now(timezone.utc)

def determine_price_action(candle_data):
    """Determine if candle is bullish or bearish"""
    open_price = candle_data['open']
    close_price = candle_data['close']
    
    if close_price > open_price:
        return "BULLISH 🟢"
    elif close_price < open_price:
        return "BEARISH 🔴"
    else:
        return "DOJI ➖"

def format_candle_times(candle_time, timeframe):
    """Calculate candle start and end times"""
    start_time = pd.to_datetime(candle_time, unit='s')
    
    # Calculate end time based on timeframe
    if timeframe == mt5.TIMEFRAME_M1:
        duration_minutes = 1
    elif timeframe == mt5.TIMEFRAME_M5:
        duration_minutes = 5
    elif timeframe == mt5.TIMEFRAME_M15:
        duration_minutes = 15
    elif timeframe == mt5.TIMEFRAME_M30:
        duration_minutes = 30
    elif timeframe == mt5.TIMEFRAME_H1:
        duration_minutes = 60
    else:
        duration_minutes = 5  # Default to 5 minutes
    
    end_time = start_time + pd.Timedelta(minutes=duration_minutes)
    
    return start_time, end_time

def get_historical_data(symbol, timeframe, num_candles=1000):
    """Fetch historical candles for model training"""
    now = datetime.now(timezone.utc)
    
    # Fetch historical data
    rates = mt5.copy_rates_from(symbol, timeframe, now, num_candles)
    
    if rates is None or len(rates) == 0:
        print("No historical data retrieved")
        return None
    
    # Convert to DataFrame
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    
    return df

def save_data_to_file(df, symbol, timeframe):
    """Save data to current directory, overwriting existing file"""
    # Create filename based on symbol and timeframe
    timeframe_str = {
        mt5.TIMEFRAME_M1: "M1",
        mt5.TIMEFRAME_M5: "M5",
        mt5.TIMEFRAME_M15: "M15",
        mt5.TIMEFRAME_M30: "M30",
        mt5.TIMEFRAME_H1: "H1"
    }.get(timeframe, "M5")
    
    filename = f"{symbol}_{timeframe_str}_data.csv"
    filepath = os.path.join(os.getcwd(), filename)
    
    # Save to CSV, overwriting existing file
    df.to_csv(filepath, index=False)
    print(f"💾 Data saved to: {filepath}")
    print(f"📊 Total candles saved: {len(df)}")
    
    return filepath

def get_latest_completed_candles(symbol, timeframe, num_candles=2, delay_seconds=2):
    """Fetch the latest completed candles for analysis"""
    time.sleep(delay_seconds)
    
    now = datetime.now(timezone.utc)
    
    # Calculate the start time of the latest complete candle based on timeframe
    if timeframe == mt5.TIMEFRAME_M5:
        minutes = now.minute // 5 * 5
        start_time = datetime(now.year, now.month, now.day, now.hour, minutes, tzinfo=timezone.utc)
    elif timeframe == mt5.TIMEFRAME_M1:
        start_time = datetime(now.year, now.month, now.day, now.hour, now.minute, tzinfo=timezone.utc)
    elif timeframe == mt5.TIMEFRAME_M15:
        minutes = now.minute // 15 * 15
        start_time = datetime(now.year, now.month, now.day, now.hour, minutes, tzinfo=timezone.utc)
    elif timeframe == mt5.TIMEFRAME_M30:
        minutes = now.minute // 30 * 30
        start_time = datetime(now.year, now.month, now.day, now.hour, minutes, tzinfo=timezone.utc)
    else:
        minutes = now.minute // 5 * 5
        start_time = datetime(now.year, now.month, now.day, now.hour, minutes, tzinfo=timezone.utc)
    
    # Fetch the latest complete candles
    rates = mt5.copy_rates_from(symbol, timeframe, start_time, num_candles + 1)
    
    if rates is None or len(rates) == 0:
        print("No data retrieved for analysis")
        return None
    
    # Convert to DataFrame
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    
    # Get the last num_candles complete candles (exclude the current incomplete one)
    completed_candles = df.tail(num_candles + 1).head(num_candles)
    
    # Reverse order so latest candle is first (Candle 1)
    return completed_candles.iloc[::-1].reset_index(drop=True)

def display_candle_analysis(symbol, timeframe, num_candles=2):
    """Display formatted candle analysis"""
    
    # Get current MT5 server time
    current_time = get_mt5_server_time()
    
    print("=" * 80)
    print(f"🕐 CURRENT MT5 SERVER TIME: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print("=" * 80)
    
    # Get latest completed candles for analysis
    latest_candles = get_latest_completed_candles(symbol, timeframe, num_candles)
    
    if latest_candles is not None:
        print(f"\n📈 LATEST {num_candles} COMPLETED CANDLES FOR {symbol}")
        print("-" * 80)
        
        for idx, (_, candle) in enumerate(latest_candles.iterrows(), 1):
            start_time, end_time = format_candle_times(candle['time'], timeframe)
            price_action = determine_price_action(candle)
            
            candle_label = "LATEST" if idx == 1 else "PREVIOUS"
            
            print(f"\n🕯️  CANDLE #{idx} ({candle_label})")
            print(f"   Time Period: {start_time.strftime('%Y-%m-%d %H:%M:%S')} → {end_time.strftime('%H:%M:%S')}")
            print(f"   Price Action: {price_action}")
            print(f"   OPEN:  {candle['open']:.5f}")
            print(f"   HIGH:  {candle['high']:.5f}")
            print(f"   LOW:   {candle['low']:.5f}")
            print(f"   CLOSE: {candle['close']:.5f}")
            print(f"   Volume: {candle['tick_volume']}")
            print(f"   Spread: {candle['spread']}")
            
            # Calculate candle body size and range
            body_size = abs(candle['close'] - candle['open'])
            candle_range = candle['high'] - candle['low']
            
            print(f"   Body Size: {body_size:.5f}")
            print(f"   Range: {candle_range:.5f}")
            print("-" * 40)
        
        # Summary
        print(f"\n📊 SUMMARY:")
        latest_candle = latest_candles.iloc[0]  # Candle 1 (latest)
        previous_candle = latest_candles.iloc[1] if len(latest_candles) > 1 else None  # Candle 2 (previous)
        
        print(f"   Latest Candle (Candle 1): {determine_price_action(latest_candle)}")
        if previous_candle is not None:
            print(f"   Previous Candle (Candle 2): {determine_price_action(previous_candle)}")
            
            # Trend analysis
            if latest_candle['close'] > previous_candle['close']:
                trend = "UPWARD TREND 📈"
            elif latest_candle['close'] < previous_candle['close']:
                trend = "DOWNWARD TREND 📉"
            else:
                trend = "SIDEWAYS 🔄"
            
            print(f"   Short-term Trend: {trend}")
    
    print("=" * 80)

def collect_and_analyze_data(symbol, timeframe, historical_candles=1000, analysis_candles=2):
    """Main function to collect historical data and perform analysis"""
    
    print("🔄 COLLECTING HISTORICAL DATA...")
    print("-" * 50)
    
    # Collect historical data for model training
    historical_data = get_historical_data(symbol, timeframe, historical_candles)
    
    if historical_data is not None:
        # Save data to file
        filepath = save_data_to_file(historical_data, symbol, timeframe)
        print(f"📈 Data range: {historical_data['time'].min()} to {historical_data['time'].max()}")
        print()
        
        # Perform candle analysis on latest completed candles
        print("🔍 PERFORMING CANDLE ANALYSIS...")
        display_candle_analysis(symbol, timeframe, analysis_candles)
        
        return filepath, historical_data
    else:
        print("❌ Failed to collect historical data")
        return None, None

# Example usage
if __name__ == "__main__":
    symbol = "EURJPY"  # Replace with your forex pair
    timeframe = mt5.TIMEFRAME_M5  # 5-minute candles
    historical_candles = 1000  # Number of candles for model training
    analysis_candles = 2  # Number of latest candles to analyze
    
    # Collect historical data and perform analysis
    filepath, data = collect_and_analyze_data(symbol, timeframe, historical_candles, analysis_candles)
    
    if filepath:
        print(f"\n✅ Process completed successfully!")
        print(f"📁 Data file: {filepath}")
        print(f"📊 Total historical candles: {len(data) if data is not None else 0}")
    
    # Disconnect from MT5
    mt5.shutdown() 