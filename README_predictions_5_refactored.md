# Fibonacci Bounce Predictor - Refactored Module

A modular, reusable library for predicting Fibonacci bounce patterns in financial data. This refactored version provides both programmatic API access and direct script execution capabilities while maintaining identical prediction results to the original implementation.

## Features

- **Modular Design**: Clean separation between configuration, initialization, and execution
- **Flexible Usage**: Both programmatic usage (import and call) and direct script execution
- **Consistent Results**: Identical prediction results to the original script
- **Comprehensive Error Handling**: Custom exceptions and graceful error handling
- **Configurable**: All file paths and parameters are configurable
- **Well-Documented**: Comprehensive API documentation and examples

## Installation

Ensure you have the required dependencies:

```bash
pip install pandas numpy tensorflow
```

## Quick Start

### Programmatic Usage

```python
from predictions_5 import load_predictor, PredictorConfig

# Basic usage with default configuration
predictor = load_predictor()
result = predictor.make_prediction()
print(f"Prediction: {result.prediction}, Probability: {result.probability:.4f}")

# Custom configuration
config = PredictorConfig(
    model_path="my_model.keras",
    data_file="my_data.csv",
    verbose=False
)
predictor = load_predictor(config)
result = predictor.make_prediction()
```

### Direct Script Execution

```bash
python predictions_5.py
```

## API Reference

### Configuration

#### PredictorConfig

Configuration class for the Fibonacci Bounce Predictor.

```python
@dataclass
class PredictorConfig:
    model_path: str = "final_dl_model.keras"
    pipeline_path: str = "feature_engineering_pipeline.pkl"
    features_path: str = "selected_features.json"
    data_file: Optional[str] = None
    symbol: str = "EURJPY"
    results_file: str = "prediction_results.json"
    verbose: bool = True
    auto_find_data: bool = True
    logging_level: str = "INFO"
```

### Core Classes

#### FibonacciBouncePredictor

Main predictor class for Fibonacci bounce pattern prediction.

**Key Methods:**
- `load_model_and_pipeline()`: Load model, pipeline, and features
- `load_data(data_file)`: Load data from CSV file
- `load_data_from_dataframe(df, symbol)`: Load data from pandas DataFrame
- `make_prediction(data)`: Make prediction using loaded model
- `get_model_info()`: Get information about loaded model and pipeline

#### PredictionResult

Data class containing prediction results.

```python
@dataclass
class PredictionResult:
    prediction_time: str
    symbol: str
    prediction: int  # 0 = No Bounce, 1 = Bounce
    probability: float  # 0.0 to 1.0
    prediction_based_on_candle: str
    prediction_candle_close: float
    latest_price: float
    latest_candle_time: str
    incomplete_candle_filtered: bool
    error: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
```

### Public API Functions

#### load_predictor(config)

Load and initialize a Fibonacci Bounce Predictor.

```python
def load_predictor(config: Optional[PredictorConfig] = None) -> FibonacciBouncePredictor
```

#### predict_from_data(data, config, symbol)

Make a prediction from pandas DataFrame data.

```python
def predict_from_data(data: pd.DataFrame, 
                     config: Optional[PredictorConfig] = None,
                     symbol: Optional[str] = None) -> PredictionResult
```

#### predict_from_file(filepath, config)

Make a prediction from a CSV file.

```python
def predict_from_file(filepath: str, 
                     config: Optional[PredictorConfig] = None) -> PredictionResult
```

#### batch_predict(data_list, config, symbols)

Make predictions for multiple datasets.

```python
def batch_predict(data_list: List[pd.DataFrame], 
                 config: Optional[PredictorConfig] = None,
                 symbols: Optional[List[str]] = None) -> List[PredictionResult]
```

## Usage Examples

### Example 1: Basic Prediction

```python
from predictions_5 import predict_from_file

# Make prediction from CSV file
result = predict_from_file("EURJPY_data.csv")

if result.error:
    print(f"Error: {result.error}")
else:
    print(f"Symbol: {result.symbol}")
    print(f"Prediction: {'BOUNCE' if result.prediction == 1 else 'NO BOUNCE'}")
    print(f"Probability: {result.probability:.4f}")
    print(f"Confidence: {'HIGH' if result.probability > 0.7 else 'MODERATE' if result.probability > 0.6 else 'LOW'}")
```

### Example 2: Custom Configuration

```python
from predictions_5 import load_predictor, PredictorConfig

# Create custom configuration
config = PredictorConfig(
    model_path="custom_model.keras",
    pipeline_path="custom_pipeline.pkl",
    features_path="custom_features.json",
    verbose=False,
    symbol="GBPUSD"
)

# Load predictor with custom config
predictor = load_predictor(config)

# Load data and make prediction
predictor.load_data("GBPUSD_data.csv")
result = predictor.make_prediction()

print(f"Prediction for {result.symbol}: {result.prediction}")
```

### Example 3: DataFrame Input

```python
import pandas as pd
from predictions_5 import predict_from_data

# Load data from CSV
data = pd.read_csv("market_data.csv")

# Make prediction
result = predict_from_data(data, symbol="USDJPY")

print(f"Prediction: {result.prediction}")
print(f"Probability: {result.probability:.4f}")
print(f"Latest Price: {result.latest_price:.5f}")
```

### Example 4: Batch Processing

```python
import pandas as pd
from predictions_5 import batch_predict

# Load multiple datasets
data1 = pd.read_csv("EURJPY_data.csv")
data2 = pd.read_csv("GBPUSD_data.csv")
data3 = pd.read_csv("USDJPY_data.csv")

# Batch predict
results = batch_predict(
    data_list=[data1, data2, data3],
    symbols=["EURJPY", "GBPUSD", "USDJPY"]
)

# Process results
for result in results:
    if result.error:
        print(f"{result.symbol}: Error - {result.error}")
    else:
        print(f"{result.symbol}: {result.prediction} (prob: {result.probability:.4f})")
```

### Example 5: Error Handling

```python
from predictions_5 import load_predictor, PredictorConfig, ModelLoadError, DataError

try:
    config = PredictorConfig(model_path="nonexistent_model.keras")
    predictor = load_predictor(config)
except ModelLoadError as e:
    print(f"Model loading failed: {e}")
except DataError as e:
    print(f"Data error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Migration Guide

### From Original Script to Refactored Module

#### Old Way (Direct Script)
```bash
python predictions_5.py
```

#### New Way (Programmatic)
```python
from predictions_5 import FibonacciBouncePredictor

predictor = FibonacciBouncePredictor()
result = predictor.run()
```

#### Key Changes

1. **Import Structure**: Import specific classes and functions instead of running the entire script
2. **Configuration**: Use `PredictorConfig` for centralized configuration management
3. **Error Handling**: Catch specific exceptions (`ModelLoadError`, `DataError`, `FeatureError`)
4. **Return Values**: Methods return structured `PredictionResult` objects instead of printing only
5. **Flexibility**: Support for both file-based and DataFrame-based data input

## File Requirements

The module expects the following files in the working directory:

- `final_dl_model.keras` - Trained TensorFlow/Keras model
- `feature_engineering_pipeline.pkl` - Feature engineering pipeline
- `selected_features.json` - List of selected features
- `*.csv` - OHLC data file (auto-discovered if not specified)

## Data Format

CSV files should contain the following columns:
- `time` - Timestamp (will be converted to datetime index)
- `open` - Opening price
- `high` - High price
- `low` - Low price
- `close` - Closing price
- `volume` or `tick_volume` or `real_volume` - Volume data (optional)

## Error Handling

The module provides comprehensive error handling with custom exceptions:

- `PredictorError` - Base exception for predictor errors
- `ModelLoadError` - Model loading failures
- `DataError` - Data processing failures
- `FeatureError` - Feature engineering failures

## Logging

The module supports configurable logging levels:
- `DEBUG` - Detailed debugging information
- `INFO` - General information (default)
- `WARNING` - Warning messages
- `ERROR` - Error messages only

## Performance Notes

- TensorFlow loading may take 30-60 seconds on first import
- Model loading is cached after first load
- Feature engineering is optimized for batch processing
- Memory usage scales with data size and number of features

## Backward Compatibility

The refactored module maintains full backward compatibility:
- Direct script execution works exactly as before
- All prediction algorithms remain unchanged
- Results are identical to the original implementation
- File formats and requirements are unchanged
